#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 MODÈLE AZR BACCARAT PREDICTOR
===============================

Modèle AZR (Absolute Zero Reasoning) adapté pour les prédictions baccarat.
Analyse les séquences PAIR/IMPAIR complètes pour prédire S (Same) ou O (Opposite).

Basé sur :
- base.txt : Base de connaissances AZR experte
- COURS_AZR : Architecture et formules mathématiques
- Logique baccarat : PAIR/IMPAIR → SYNC/DESYNC → S/O

Version : 1.0
Date : Décembre 2024

STRUCTURE DU CODE PAR CATÉGORIES :
=================================
1. 📦 IMPORTS ET CONFIGURATION
2. 📋 STRUCTURES DE DONNÉES
3. 🎮 INTERFACE GRAPHIQUE ULTRA-SIMPLIFIÉE
4. 🧠 CLASSE PRINCIPALE AZR
5. 🎯 INTERFACE PRINCIPALE (receive_hand_data)
6. 🔮 GÉNÉRATION DE PRÉDICTIONS
7. 🎭 RÔLE PROPOSEUR AZR
8. 🔧 RÔLE RÉSOLVEUR AZR
9. 📊 APPRENTISSAGE ADAPTATIF
10. 📈 MÉTRIQUES ET STATISTIQUES
11. 🎲 GÉNÉRATEUR DE DONNÉES BACCARAT
12. 📂 CHARGEUR DE DONNÉES
13. 🚀 FONCTIONS UTILITAIRES ET MAIN
"""

# ============================================================================
# 📦 1. IMPORTS ET CONFIGURATION
# ============================================================================

import numpy as np
import json
import time
import math
import random
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from typing import List, Dict, Tuple, Optional, Any, Iterator
from collections import deque, defaultdict
from dataclasses import dataclass
from datetime import datetime
import logging
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import gc
import psutil
import sys

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# 📋 2. STRUCTURES DE DONNÉES
# ============================================================================

@dataclass
class BaccaratHand:
    """Représente une manche de baccarat avec ses métadonnées"""
    pb_hand_number: int
    result: str  # 'PLAYER', 'BANKER', 'TIE'
    parity: str  # 'PAIR', 'IMPAIR'
    sync_state: str  # 'SYNC', 'DESYNC'
    so_conversion: str  # 'S', 'O', '--'
    combined_state: str = ""  # 'PAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC', 'IMPAIR_DESYNC'

# ============================================================================
# ⚙️ 3. CONFIGURATION CENTRALISÉE - TOUS LES PARAMÈTRES
# ============================================================================

@dataclass
class AZRConfig:
    """
    Configuration centralisée de tous les paramètres du système AZR Baccarat

    ORGANISATION PAR CATÉGORIES :
    - 🎲 BACCARAT : Règles du jeu et configuration des parties
    - 🧠 AZR : Hyperparamètres du modèle de prédiction
    - 🎯 PRÉDICTIONS : Règles découvertes et seuils de confiance
    - 🎮 INTERFACE : Paramètres de l'interface graphique
    - 📊 ANALYSE : Paramètres d'analyse et statistiques
    - 💾 FICHIERS : Configuration des fichiers et sauvegarde
    """

    # ========================================================================
    # 🎲 BACCARAT - RÈGLES DU JEU ET CONFIGURATION
    # ========================================================================

    # Configuration du sabot
    num_decks: int = 8                          # Nombre de jeux de cartes
    total_cards: int = 416                      # Total cartes (8 × 52)
    cut_card_position: int = 312                # Position cut card (75% de 416)
    cut_card_percentage: float = 0.75           # Pourcentage de pénétration

    # Cartes et valeurs
    card_suits: List[str] = None                # Couleurs des cartes
    card_values: List[str] = None               # Valeurs des cartes
    face_cards: List[str] = None                # Cartes figures

    # Règles de brûlage
    min_burn_cards: int = 2                     # Minimum cartes brûlées
    max_burn_cards: int = 11                    # Maximum cartes brûlées
    burn_value_zero_additional: int = 10        # Cartes supplémentaires si valeur 0
    burn_value_ace: int = 1                     # Valeur As pour brûlage

    # Règles du baccarat
    natural_threshold: int = 8                  # Seuil pour naturel (8 ou 9)
    player_draw_threshold: int = 5              # Player tire si ≤ 5
    banker_stand_threshold: int = 7             # Banker ne tire jamais si ≥ 7

    # Limites de partie
    default_hands_per_game: int = 60            # Manches P/B par défaut
    max_hands_per_game: int = 80                # Maximum manches P/B possible
    min_hands_per_game: int = 40                # Minimum manches P/B attendu

    # ========================================================================
    # 🧠 AZR - HYPERPARAMÈTRES DU MODÈLE CŒUR
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - MODÈLE AZR PRINCIPAL
    # Cette section contient UNIQUEMENT les paramètres du cœur AZR
    # Modification = Impact direct sur les performances de prédiction
    # ========================================================================

    # Apprentissage
    learning_rate: float = 1e-4                 # Taux d'apprentissage
    adaptation_rate: float = 0.1                # Taux d'adaptation
    baseline_alpha: float = 0.99                # Facteur de lissage baseline
    lambda_coefficient: float = 1.0             # Coefficient d'équilibrage

    # Confiance et seuils
    confidence_threshold: float = 0.4           # Seuil confiance minimum
    base_confidence: float = 0.5                # Confiance de base
    pattern_bonus: float = 0.3                  # Bonus pour patterns détectés
    confidence_weight: float = 0.4              # Poids de la confiance
    high_confidence_threshold: float = 0.6      # Seuil haute confiance
    low_confidence_threshold: float = 0.3       # Seuil basse confiance

    # ========================================================================
    # 💻 OPTIMISATIONS MATÉRIELLES - CPU 8 CŒURS + 28GB RAM
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - OPTIMISATIONS HARDWARE
    # Configuration spécifique pour notre machine (8 cœurs CPU, 28GB RAM, pas GPU)
    # ========================================================================

    # Configuration matérielle
    cpu_cores: int = 8                          # Nombre de cœurs CPU disponibles
    max_ram_gb: int = 24                        # RAM maximale utilisable (marge sécurité)
    has_gpu: bool = False                       # Pas de GPU disponible

    # Parallélisation CPU
    parallel_rollouts: bool = True              # Activer rollouts parallèles
    thread_pool_size: int = 8                   # Taille pool threads (= nb cœurs)
    process_pool_size: int = 8                  # Taille pool processus pour génération massive

    # Rollouts optimisés CPU
    n_rollouts: int = 16                        # Nombre rollouts (2 par cœur)
    rollout_temperature: float = 0.7            # Température des rollouts
    rollout_step_size: float = 0.15             # Taille de pas rollout
    rollout_random_factor: float = 0.1          # Facteur aléatoire rollout

    # Gestion mémoire optimisée
    batch_size_generation: int = 25000          # Parties par batch (optimisé 28GB)
    memory_efficient_mode: bool = True          # Mode économie mémoire
    gc_frequency: int = 10000                   # Fréquence garbage collection
    max_memory_usage_percent: float = 0.85      # Limite utilisation mémoire (85%)

    # Monitoring performance
    memory_monitoring: bool = True              # Surveillance mémoire active
    cpu_monitoring: bool = True                 # Surveillance CPU active
    performance_logging: bool = True            # Logging performance détaillé

    # Patterns et mémoire
    pattern_min_length: int = 3                 # Longueur minimum des patterns
    max_sequence_length: int = 1000             # Longueur max séquence
    memory_buffer_size: int = 500               # Taille buffer mémoire
    learnability_buffer_size: int = 100         # Buffer scores learnability
    diversity_buffer_size: int = 100            # Buffer scores diversité

    # ========================================================================
    # 🔢 CONSTANTES NUMÉRIQUES - VALEURS CENTRALISÉES
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - CENTRALISATION COMPLÈTE
    # Toutes les valeurs numériques utilisées dans les méthodes
    # AUCUNE valeur codée en dur autorisée dans les méthodes
    # ========================================================================

    # Valeurs de base
    zero_value: int = 0                         # Valeur zéro
    one_value: int = 1                          # Valeur un
    two_value: int = 2                          # Valeur deux
    three_value: int = 3                        # Valeur trois
    four_value: int = 4                         # Valeur quatre
    five_value: int = 5                         # Valeur cinq
    six_value: int = 6                          # Valeur six
    seven_value: int = 7                        # Valeur sept
    eight_value: int = 8                        # Valeur huit
    nine_value: int = 9                         # Valeur neuf
    ten_value: int = 10                         # Valeur dix
    eleven_value: int = 11                      # Valeur onze

    # Modulo et calculs
    modulo_ten: int = 10                        # Modulo pour calculs baccarat
    percentage_hundred: int = 100               # Pourcentage base 100

    # Seuils et limites
    min_hands_for_prediction: int = 2           # Minimum manches pour prédiction
    min_accuracy_history: int = 5               # Minimum historique précision
    min_accuracy_trend: int = 10                # Minimum pour tendance précision

    # Intervalles et fréquences
    progress_interval_small: int = 20           # Intervalle progression petit
    progress_interval_medium: int = 100         # Intervalle progression moyen
    progress_interval_large: int = 1000         # Intervalle progression grand
    progress_interval_massive: int = 10000      # Intervalle progression massif

    # Facteurs et multiplicateurs
    step_increment: float = 0.1                 # Incrément de pas standard
    small_increment: float = 0.02               # Petit incrément
    large_increment: float = 1.0                # Grand incrément

    # Seuils de performance
    low_performance_threshold: float = -0.01    # Seuil performance faible
    high_performance_threshold: float = 0.01    # Seuil performance élevée

    # Facteurs d'ajustement
    reduction_factor: float = 0.95              # Facteur de réduction
    increase_factor: float = 1.02               # Facteur d'augmentation

    # Limites de configuration
    max_pattern_length: int = 5                 # Longueur max pattern
    min_pattern_length_limit: int = 2           # Limite min longueur pattern
    max_confidence_limit: float = 0.8           # Limite max confiance

    # ========================================================================
    # 🔧 VALEURS SPÉCIFIQUES CLUSTERS AZR
    # ========================================================================

    # Timing optimal par phase (170ms total)
    cluster_analysis_time_ms: int = 60          # Phase analyse (0-60ms)
    cluster_generation_time_ms: int = 50        # Phase génération (60-110ms)
    cluster_prediction_time_ms: int = 60        # Phase prédiction (110-170ms)
    cluster_total_time_ms: int = 170            # Temps total optimal

    # Seuils d'alerte consécutives
    impair_alert_threshold_medium: int = 3      # Alerte moyenne IMPAIR
    impair_alert_threshold_high: int = 5        # Alerte élevée IMPAIR
    pair_alert_threshold_medium: int = 5        # Alerte moyenne PAIR
    pair_alert_threshold_high: int = 7          # Alerte élevée PAIR

    # Probabilités de rupture
    rupture_probability_base: float = 0.5       # Probabilité base rupture
    rupture_probability_impair_base: float = 0.7  # Base rupture IMPAIR
    rupture_probability_pair_base: float = 0.6    # Base rupture PAIR
    rupture_probability_increment: float = 0.05   # Incrément par séquence
    rupture_probability_max: float = 0.95         # Maximum rupture

    # Seuils de fiabilité corrélations
    correlation_reliability_threshold: float = 0.15  # Seuil écart-type fiable
    correlation_exploitation_threshold: float = 0.2  # Seuil exploitation

    # Génération de séquences candidates
    candidate_sequences_count: int = 4          # Nombre séquences candidates
    max_probability_estimate: float = 0.85      # Estimation probabilité max
    alternative_probability_estimate: float = 0.70  # Estimation alternative
    rupture_probability_estimate: float = 0.60     # Estimation rupture
    conservative_probability_estimate: float = 0.45 # Estimation conservatrice

    # Longueurs de séquences
    default_sequence_length: int = 3            # Longueur séquence par défaut
    extended_sequence_length: int = 4           # Longueur étendue si transitions

    # ========================================================================
    # 🧮 FORMULES MATHÉMATIQUES AZR - SECTION DÉDIÉE
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - FORMULES MATHÉMATIQUES COMPLÈTES
    # Toutes les équations et formules du modèle AZR centralisées
    # Basées sur les recherches académiques et COURS_AZR
    # Référence : base.txt + Formules_Mathematiques_AZR_Complete.md
    # ========================================================================

    # 1. FONCTION OBJECTIF PRINCIPALE AZR
    # J(θ) = E_z~p(z) E_τ~π_θ^propose(·|z) E_(x,y*)~f_e(·|τ) [r_e^propose(τ,π_θ) + λ × r_e^solve(y,y*)]
    objective_lambda_coefficient: float = 1.0   # λ - Coefficient d'équilibrage propose/solve

    # 2. FORMULE DE LEARNABILITY (CŒUR AZR)
    # r_e^propose = 1 - r̄_solve si r̄_solve ∉ {0,1}, sinon 0
    learnability_min_threshold: float = 0.0     # Seuil minimum learnability
    learnability_max_threshold: float = 1.0     # Seuil maximum learnability
    learnability_zone_proximal: float = 1.0     # Facteur zone développement proximal

    # 3. RÉCOMPENSES AZR
    # r^solve_e(y,y*) = 1 si y = y*, sinon 0 (correctness)
    reward_correct: float = 1.0                 # Récompense prédiction correcte
    reward_incorrect: float = 0.0               # Récompense prédiction incorrecte

    # r^propose_e basée sur learnability optimale
    reward_learnability_optimal: float = 0.5    # Learnability optimale (50% difficulté)
    reward_learnability_factor: float = 2.0     # Facteur amplification learnability

    # 4. GRADIENTS REINFORCE++ ADAPTÉS
    # ∇_θ J^propose = E[∇_θ log π_θ^propose(τ|z) × (r^propose_e - b^propose)]
    gradient_propose_weight: float = 1.0        # Poids gradient proposition
    gradient_solve_weight: float = 1.0          # Poids gradient résolution

    # 5. BASELINES POUR RÉDUCTION VARIANCE
    # b^propose = E_τ~π_θ^propose [r^propose_e(τ,π_θ)]
    baseline_momentum: float = 0.99             # Momentum mise à jour baselines
    baseline_epsilon: float = 1e-8              # Epsilon stabilité numérique

    # 6. MÉTRIQUES DE QUALITÉ AZR
    # Diversité : H(π_θ^propose) = -Σ π_θ^propose(τ|z) log π_θ^propose(τ|z)
    diversity_entropy_weight: float = 0.1       # Poids entropie diversité
    diversity_min_entropy: float = 0.5          # Entropie minimum requise

    # Complexité : C(τ) = mesure de difficulté de la tâche τ
    complexity_base_factor: float = 1.0         # Facteur base complexité
    complexity_scaling: float = 0.1             # Facteur d'échelle complexité

    # 7. OPTIMISATION ADAPTATIVE
    # α_t = α_0 × (1 + β × t)^(-γ) (learning rate decay)
    lr_decay_beta: float = 0.1                  # Facteur β decay
    lr_decay_gamma: float = 0.5                 # Exposant γ decay
    lr_min_threshold: float = 1e-6              # Learning rate minimum

    # 8. TEMPÉRATURE ET EXPLORATION
    # π_θ(a|s) = softmax(f_θ(s)/T) avec température T
    temperature_initial: float = 1.0            # Température initiale
    temperature_decay: float = 0.995            # Facteur decay température
    temperature_min: float = 0.1                # Température minimum

    # 9. FORMULES DE FUSION PRÉDICTIONS
    # P_final = w_combined × P_combined + w_classic × P_classic
    fusion_combined_weight: float = 0.7         # Poids prédiction combinée
    fusion_classic_weight: float = 0.3          # Poids prédiction classique
    fusion_confidence_threshold: float = 0.6    # Seuil fusion haute confiance

    # 10. MÉTRIQUES DE PERFORMANCE AZR
    # Accuracy = (TP + TN) / (TP + TN + FP + FN)
    performance_smoothing: float = 0.95         # Lissage métriques performance
    performance_window_size: int = 100          # Fenêtre calcul performance

    # 11. ADAPTATION DYNAMIQUE PARAMÈTRES
    # Δθ = -η × ∇_θ J + μ × Δθ_prev (momentum)
    adaptation_momentum: float = 0.9            # Momentum adaptation
    adaptation_threshold: float = 0.01          # Seuil changement significatif
    adaptation_max_change: float = 0.1          # Changement maximum par étape

    # 12. PARAMÈTRES ROLLOUTS SÉQUENCE COMPLÈTE
    # Influences des états combinés découverts (révolutionnaire)
    combined_pair_sync_influence: float = 0.12      # PAIR_SYNC → O (61.2% - 50% = 11.2%)
    combined_impair_sync_influence: float = 0.011   # IMPAIR_SYNC → S (51.1% - 50% = 1.1%)
    combined_pair_desync_influence: float = 0.032   # PAIR_DESYNC → O (53.2% - 50% = 3.2%)
    combined_impair_desync_influence: float = 0.004 # IMPAIR_DESYNC → O (50.4% - 50% = 0.4%)

    # Paramètres d'analyse temporelle
    step_increment: float = 0.05                 # Incrément pour transitions
    small_increment: float = 0.02                # Petit incrément pour ajustements

    # Seuils de performance pour adaptation
    low_performance_threshold: float = -0.01     # Seuil performance en baisse
    high_performance_threshold: float = 0.01     # Seuil performance en hausse
    min_accuracy_trend: int = 10                 # Minimum points pour tendance

    # ========================================================================
    # 🎯 AZR - RÈGLES DE PRÉDICTION DÉCOUVERTES (INDEX COMBINÉ)
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - INTELLIGENCE AZR RÉVOLUTIONNAIRE
    # 🔬 MODE EXPLORATION AVEUGLE ACTIVÉ - DÉCOUVERTES TEMPORAIREMENT MASQUÉES
    # Les rollouts explorent sans connaître les patterns découverts pour tester
    # leur capacité à redécouvrir indépendamment les règles révolutionnaires
    # ========================================================================

    # Activation/désactivation des découvertes pour exploration aveugle
    use_combined_index_discovery: bool = False      # 🔬 DÉSACTIVÉ pour exploration aveugle

    # Règles index combiné (découvertes révolutionnaires - MASQUÉES)
    combined_prediction_rules: Dict[str, Dict[str, float]] = None

    # Seuils de fusion des prédictions
    combined_confidence_threshold: float = 0.6  # Seuil pour privilégier index combiné
    combined_moderate_threshold: float = 0.3    # Seuil confiance modérée
    combined_agreement_bonus: float = 0.1       # Bonus si accord des méthodes

    # Probabilités et ajustements
    probability_clamp_min: float = 0.0          # Minimum probabilité
    probability_clamp_max: float = 1.0          # Maximum probabilité
    probability_neutral: float = 0.5            # Probabilité neutre

    # ========================================================================
    # 🎮 INTERFACE - PARAMÈTRES INTERFACE GRAPHIQUE
    # ========================================================================

    # Dimensions fenêtre
    window_width: int = 800                     # Largeur fenêtre principale
    window_height: int = 600                    # Hauteur fenêtre principale
    stats_window_width: int = 600               # Largeur fenêtre stats
    stats_window_height: int = 500              # Hauteur fenêtre stats

    # Couleurs interface
    player_color: str = "#4A90E2"               # Couleur bouton Player (bleu)
    banker_color: str = "#E74C3C"               # Couleur bouton Banker (rouge)
    tie_color: str = "#27AE60"                  # Couleur bouton Tie (vert)
    background_color: str = "#2C3E50"           # Couleur arrière-plan
    text_color: str = "#FFFFFF"                 # Couleur texte

    # Polices
    main_font: Tuple[str, int] = ("Arial", 12)  # Police principale
    title_font: Tuple[str, int, str] = ("Arial", 14, "bold")  # Police titres
    mono_font: Tuple[str, int] = ("Courier", 10)  # Police monospace

    # Timing et animations
    update_delay_ms: int = 100                  # Délai mise à jour interface (ms)
    animation_duration_ms: int = 200            # Durée animations (ms)

    # ========================================================================
    # 📊 ANALYSE - PARAMÈTRES ANALYSE ET STATISTIQUES
    # ========================================================================

    # Génération de données (optimisée CPU/RAM)
    default_training_games: int = 100000        # Parties d'entraînement (optimisé 28GB)
    default_test_games: int = 10000             # Parties de test (optimisé)
    progress_report_interval: int = 5000        # Intervalle rapport (optimisé gros volumes)

    # Analyse statistique
    min_sample_size: int = 50                   # Taille minimum échantillon
    significance_threshold: float = 0.05        # Seuil significativité statistique
    confidence_interval: float = 0.95           # Intervalle de confiance

    # Performance et métriques
    accuracy_window_size: int = 100             # Fenêtre calcul précision
    trend_analysis_window: int = 50             # Fenêtre analyse tendance
    performance_history_size: int = 1000        # Taille historique performance

    # ========================================================================
    # 💾 FICHIERS - CONFIGURATION SAUVEGARDE ET FORMATS
    # ========================================================================

    # Formats de fichiers
    default_encoding: str = "utf-8"             # Encodage par défaut
    json_indent: int = 2                        # Indentation JSON
    csv_delimiter: str = ","                    # Délimiteur CSV

    # Noms de fichiers par défaut
    training_data_prefix: str = "azr_training"  # Préfixe données entraînement
    test_data_prefix: str = "azr_test"          # Préfixe données test
    model_state_prefix: str = "azr_model"       # Préfixe état modèle
    formatted_games_prefix: str = "parties_formatees"  # Préfixe parties formatées

    # Limites de fichiers
    max_file_size_mb: int = 100                 # Taille max fichier (MB)
    backup_threshold_mb: int = 50               # Seuil création backup (MB)

    # ========================================================================
    # 💾 PERSISTANCE AZR - MÉMOIRE INTELLIGENTE CONTINUE
    # ========================================================================

    # Fichiers de persistance
    azr_state_file: str = "azr_intelligence_state.json"     # État principal AZR
    azr_discoveries_file: str = "azr_discoveries.json"      # Découvertes validées
    azr_baselines_file: str = "azr_baselines.json"         # Baselines adaptatifs
    azr_backup_dir: str = "azr_backups"                     # Dossier sauvegardes

    # Paramètres de sauvegarde
    auto_save_enabled: bool = True                          # Sauvegarde automatique
    save_frequency: int = 10                                # Sauvegarder toutes les N prédictions
    max_backups: int = 10                                   # Nombre max de sauvegardes
    compress_backups: bool = True                           # Compression des sauvegardes

    # Versioning intelligent
    version_discoveries: bool = True                        # Versioning des découvertes
    validate_on_load: bool = True                          # Validation au chargement
    merge_discoveries: bool = True                         # Fusion intelligente découvertes

    def __post_init__(self):
        """Initialise les valeurs par défaut et règles découvertes"""

        # Initialiser les listes de cartes
        if self.card_suits is None:
            self.card_suits = ['♠', '♥', '♦', '♣']

        if self.card_values is None:
            self.card_values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']

        if self.face_cards is None:
            self.face_cards = ['J', 'Q', 'K', '10']

        # Initialiser les règles de prédiction découvertes
        if self.combined_prediction_rules is None:
            self.combined_prediction_rules = {
                'IMPAIR_SYNC': {
                    'S_rate': 0.511,  # 51.1% - OPTIMAL pour S
                    'O_rate': 0.489,  # 48.9%
                    'preferred_prediction': 'S',
                    'confidence_bonus': 0.2  # Bonus de confiance
                },
                'PAIR_SYNC': {
                    'S_rate': 0.388,  # 38.8% - PIRE pour S
                    'O_rate': 0.612,  # 61.2% - OPTIMAL pour O
                    'preferred_prediction': 'O',
                    'confidence_bonus': 0.3  # Plus fort bonus (meilleur signal)
                },
                'PAIR_DESYNC': {
                    'S_rate': 0.468,  # 46.8%
                    'O_rate': 0.532,  # 53.2%
                    'preferred_prediction': 'O',
                    'confidence_bonus': 0.1  # Bonus modéré
                },
                'IMPAIR_DESYNC': {
                    'S_rate': 0.496,  # 49.6%
                    'O_rate': 0.504,  # 50.4%
                    'preferred_prediction': 'O',
                    'confidence_bonus': 0.05  # Bonus faible (quasi-aléatoire)
                }
            }

# ============================================================================
# 🧠 3. ARCHITECTURE CLUSTER AZR - ROLLOUTS SPÉCIALISÉS
# ============================================================================
"""
ARCHITECTURE CLUSTER AZR - SYSTÈME ROLLOUTS DISTRIBUÉS

Basé sur RESUME_COMPLET_3_ROLLOUTS_CLUSTER.txt et RECHERCHES_ROLLOUTS_CLUSTERS_INTERNET.txt

ARCHITECTURE VALIDÉE :
- 8 clusters (1 par cœur CPU)
- 3 rollouts par cluster (24 rollouts total)
- Pipeline séquentiel intra-cluster + parallèle inter-clusters
- Communication shared memory + message passing
- Synchronisation barrières entre phases + asynchrone intra-phase
- Timing optimal : 170ms total pour cycle complet

STANDARDS INDUSTRIE APPLIQUÉS :
- Master-Worker architecture (référence Tesauro)
- 90% efficacité parallélisation attendue
- Overlap computation/communication
- Load balancing automatique

ROLLOUTS SPÉCIALISÉS PAR CLUSTER :
1. Rollout Analyseur : Analyse séquence complète depuis brûlage
2. Rollout Générateur : Génération 4 séquences candidates
3. Rollout Prédicteur : Sélection séquence optimale

COMMUNICATION OPTIMISÉE :
- Shared memory locale par cluster
- Message passing global entre clusters
- Barrières synchronisation entre phases
- Données minimales (séquences courtes)
"""

# ============================================================================
# 🎯 CLUSTER AZR - IMPLÉMENTATION COMPLÈTE
# ============================================================================

class AZRCluster:
    """
    Cluster AZR - Unité de base du système distribué

    Chaque cluster contient 3 rollouts spécialisés :
    1. Rollout Analyseur : Analyse séquence complète depuis brûlage
    2. Rollout Générateur : Génération 4 séquences candidates
    3. Rollout Prédicteur : Sélection séquence optimale

    Architecture basée sur standards industrie et recherches approfondies.
    """

    def __init__(self, cluster_id: int, config: AZRConfig):
        self.cluster_id = cluster_id
        self.config = config

        # Timing optimal par phase (170ms total)
        self.phase_timings = {
            'analysis': self.config.cluster_analysis_time_ms,      # 0-60ms : Analyse complète
            'generation': self.config.cluster_generation_time_ms,  # 60-110ms : Génération séquences
            'prediction': self.config.cluster_prediction_time_ms   # 110-170ms : Prédiction finale
        }

        # Communication intra-cluster (shared memory)
        self.shared_memory = {
            'analyzer_report': None,
            'generated_sequences': None,
            'final_prediction': None,
            'cluster_confidence': 0.0
        }

        # Métriques performance cluster
        self.performance_metrics = {
            'total_predictions': 0,
            'successful_predictions': 0,
            'average_confidence': 0.0,
            'timing_efficiency': 0.0
        }

    def execute_cluster_pipeline(self, standardized_sequence: Dict) -> Dict:
        """
        Pipeline principal du cluster AZR

        Exécute les 3 rollouts en séquence avec timing optimal :
        1. Rollout Analyseur (0-60ms)
        2. Rollout Générateur (60-110ms)
        3. Rollout Prédicteur (110-170ms)

        Args:
            standardized_sequence: Séquence complète depuis brûlage

        Returns:
            Dict: Prédiction finale du cluster avec confiance
        """
        import time
        start_time = time.time()

        try:
            # Phase 1 : Rollout Analyseur (0-60ms)
            analyzer_start = time.time()
            analyzer_report = self._rollout_analyzer(standardized_sequence)
            analyzer_time = (time.time() - analyzer_start) * 1000

            self.shared_memory['analyzer_report'] = analyzer_report

            # Phase 2 : Rollout Générateur (60-110ms)
            generator_start = time.time()
            generated_sequences = self._rollout_generator(analyzer_report)
            generator_time = (time.time() - generator_start) * 1000

            self.shared_memory['generated_sequences'] = generated_sequences

            # Phase 3 : Rollout Prédicteur (110-170ms)
            predictor_start = time.time()
            final_prediction = self._rollout_predictor(generated_sequences, analyzer_report)
            predictor_time = (time.time() - predictor_start) * 1000

            self.shared_memory['final_prediction'] = final_prediction

            # Calcul timing total
            total_time = (time.time() - start_time) * 1000

            # Mise à jour métriques performance
            self._update_performance_metrics(final_prediction, total_time)

            # Résultat final avec métadonnées cluster
            cluster_result = {
                'cluster_id': self.cluster_id,
                'prediction': final_prediction,
                'confidence': self.shared_memory['cluster_confidence'],
                'timing_breakdown': {
                    'analyzer_ms': analyzer_time,
                    'generator_ms': generator_time,
                    'predictor_ms': predictor_time,
                    'total_ms': total_time
                },
                'performance_metrics': self.performance_metrics.copy()
            }

            return cluster_result

        except Exception as e:
            logger.error(f"Erreur cluster {self.cluster_id}: {e}")
            return {
                'cluster_id': self.cluster_id,
                'prediction': None,
                'confidence': 0.0,
                'error': str(e)
            }

    def _rollout_analyzer(self, standardized_sequence: Dict) -> Dict:
        """
        Rollout 1 Analyseur - Analyse complète séquence depuis brûlage

        Analyse TOUTE la séquence depuis le brûlage avec les 5 indices :
        1. Index IMPAIR/PAIR
        2. Index DESYNC/SYNC
        3. Index COMBINÉ
        4. Index P/B/T
        5. Index S/O

        Args:
            standardized_sequence: Séquence complète depuis brûlage

        Returns:
            Dict: Analyse complète des 5 indices pour rollout générateur
        """
        try:
            analysis_start = time.time()

            # Extraction données complètes depuis brûlage
            hands_data = standardized_sequence.get('hands_history', [])
            if not hands_data:
                return {'error': 'Aucune donnée historique disponible'}

            # ================================================================
            # INDEX 1 : ANALYSE IMPAIR/PAIR COMPLÈTE (15ms)
            # ================================================================

            impair_pair_analysis = self._analyze_complete_impair_pair_index(hands_data)

            # ================================================================
            # INDEX 2 : ANALYSE DESYNC/SYNC COMPLÈTE (10ms)
            # ================================================================

            desync_sync_analysis = self._analyze_complete_desync_sync_index(hands_data)

            # ================================================================
            # INDEX 3 : ANALYSE COMBINÉ COMPLÈTE (10ms)
            # ================================================================

            combined_analysis = self._analyze_complete_combined_index(
                impair_pair_analysis, desync_sync_analysis, hands_data
            )

            # ================================================================
            # INDEX 4 : ANALYSE P/B/T COMPLÈTE (15ms)
            # ================================================================

            pbt_analysis = self._analyze_complete_pbt_index(hands_data)

            # ================================================================
            # INDEX 5 : ANALYSE S/O COMPLÈTE (10ms)
            # ================================================================

            so_analysis = self._analyze_complete_so_index(hands_data)

            # ================================================================
            # SYNTHÈSE FINALE ET ZONES D'OPPORTUNITÉ
            # ================================================================

            synthesis = self._synthesize_complete_analysis({
                'impair_pair': impair_pair_analysis,
                'desync_sync': desync_sync_analysis,
                'combined': combined_analysis,
                'pbt': pbt_analysis,
                'so': so_analysis
            })

            # Rapport final enrichi pour générateur
            analyzer_report = {
                'indices_analysis': {
                    'impair_pair': impair_pair_analysis,
                    'desync_sync': desync_sync_analysis,
                    'combined': combined_analysis,
                    'pbt': pbt_analysis,
                    'so': so_analysis
                },
                'synthesis': synthesis,
                'sequence_metadata': {
                    'total_hands_analyzed': len(hands_data),
                    'analysis_quality': synthesis.get('analysis_quality', 0.0),
                    'dominant_correlations': synthesis.get('dominant_correlations', []),
                    'high_confidence_zones': synthesis.get('high_confidence_zones', [])
                },
                'execution_time_ms': (time.time() - analysis_start) * 1000,
                'cluster_id': self.cluster_id
            }

            return analyzer_report

        except Exception as e:
            logger.error(f"Erreur rollout analyzer cluster {self.cluster_id}: {e}")
            return {'error': str(e)}

    def _rollout_generator(self, analyzer_report: Dict) -> List[Dict]:
        """
        Rollout 2 Générateur - Génération séquences candidates basées sur analyse complète

        Exploite l'analyse complète des 5 indices pour génération optimale.
        Génère 4 séquences candidates avec stratégies distinctes.

        Args:
            analyzer_report: Rapport complet du rollout analyseur avec 5 indices

        Returns:
            List[Dict]: 4 séquences candidates avec probabilités
        """
        try:
            if 'error' in analyzer_report:
                return []

            # Extraction analyse complète des 5 indices
            indices_analysis = analyzer_report.get('indices_analysis', {})
            synthesis = analyzer_report.get('synthesis', {})
            sequence_metadata = analyzer_report.get('sequence_metadata', {})

            # Définition espace de génération basé sur analyse complète
            generation_space = self._define_complete_generation_space(
                indices_analysis, synthesis, sequence_metadata
            )

            # Génération 4 séquences candidates avec nouvelles stratégies
            candidates = []

            # Séquence 1 : Exploitation corrélations IMPAIR/PAIR dominantes
            seq1 = self._generate_impair_pair_optimized_sequence(generation_space)
            candidates.append({
                'sequence_data': seq1,
                'estimated_probability': self.config.max_probability_estimate,
                'strategy': 'impair_pair_optimized',
                'justification': 'Exploitation corrélations IMPAIR/PAIR → P/B/T'
            })

            # Séquence 2 : Exploitation synchronisation SYNC/DESYNC
            seq2 = self._generate_sync_based_sequence(generation_space)
            candidates.append({
                'sequence_data': seq2,
                'estimated_probability': self.config.alternative_probability_estimate,
                'strategy': 'sync_based',
                'justification': 'Exploitation patterns synchronisation'
            })

            # Séquence 3 : Exploitation index combiné dominant
            seq3 = self._generate_combined_index_sequence(generation_space)
            candidates.append({
                'sequence_data': seq3,
                'estimated_probability': self.config.rupture_probability_estimate,
                'strategy': 'combined_index',
                'justification': 'Exploitation index combiné dominant'
            })

            # Séquence 4 : Exploitation patterns S/O
            seq4 = self._generate_so_pattern_sequence(generation_space)
            candidates.append({
                'sequence_data': seq4,
                'estimated_probability': self.config.conservative_probability_estimate,
                'strategy': 'so_pattern',
                'justification': 'Exploitation patterns S/O (Same/Opposite)'
            })

            # Enrichissement séquences avec tous les indices
            enriched_candidates = self._enrich_sequences_with_complete_indexes(candidates, indices_analysis)

            return enriched_candidates

        except Exception as e:
            logger.error(f"Erreur rollout generator cluster {self.cluster_id}: {e}")
            return []

    def _rollout_predictor(self, generated_sequences: List[Dict], analyzer_report: Dict) -> Dict:
        """
        Rollout 3 Prédicteur - Sélection séquence optimale finale

        Évalue et sélectionne la meilleure séquence parmi les candidates.
        Utilise critères S/O prioritaires et intelligence de sélection.

        Args:
            generated_sequences: Séquences candidates du générateur
            analyzer_report: Rapport original de l'analyseur

        Returns:
            Dict: Prédiction finale avec confiance et justification
        """
        try:
            if not generated_sequences:
                return {
                    'prediction': None,
                    'confidence': 0.0,
                    'error': 'Aucune séquence candidate disponible'
                }

            # Évaluation détaillée de chaque séquence candidate
            evaluated_sequences = []

            for sequence in generated_sequences:
                evaluation = self._evaluate_sequence_quality(sequence, analyzer_report)
                sequence['evaluation'] = evaluation
                evaluated_sequences.append(sequence)

            # Sélection de la meilleure séquence
            best_sequence = self._select_best_sequence(evaluated_sequences)

            # Calcul confiance finale du cluster
            cluster_confidence = self._calculate_cluster_confidence(best_sequence, analyzer_report)
            self.shared_memory['cluster_confidence'] = cluster_confidence

            # Prédiction finale
            final_prediction = {
                'sequence': best_sequence['sequence_data'],
                'strategy': best_sequence['strategy'],
                'justification': best_sequence['justification'],
                'estimated_probability': best_sequence['estimated_probability'],
                'evaluation_score': best_sequence['evaluation']['total_score'],
                'cluster_confidence': cluster_confidence,
                'next_hand_prediction': self._extract_next_hand_prediction(best_sequence)
            }

            return final_prediction

        except Exception as e:
            logger.error(f"Erreur rollout predictor cluster {self.cluster_id}: {e}")
            return {
                'prediction': None,
                'confidence': 0.0,
                'error': str(e)
            }

    # ========================================================================
    # MÉTHODES D'ANALYSE COMPLÈTE DES 5 INDICES
    # ========================================================================

    def _analyze_complete_impair_pair_index(self, hands_data: List) -> Dict:
        """
        Analyse complète INDEX 1 : IMPAIR/PAIR

        Calcule pour CHAQUE manche depuis le brûlage :
        - Position dans la séquence (impaire ou paire)
        - Corrélation avec les résultats P/B/T
        - Patterns de distribution et fréquences
        """
        impair_pair_analysis = {
            'sequence_positions': [],      # [1,2,3,4,5,6,7,8,9,10,...]
            'position_types': [],          # ['IMPAIR','PAIR','IMPAIR','PAIR',...]
            'pbt_outcomes': [],            # ['P','B','P','T','B','P',...]
            'correlations': {},            # Corrélations IMPAIR/PAIR → P/B/T
            'consecutive_patterns': {},    # Séquences consécutives
            'distribution_stats': {}       # Statistiques de distribution
        }

        # Calcul pour chaque manche depuis brûlage
        for hand_number in range(1, len(hands_data) + 1):
            position_type = 'IMPAIR' if hand_number % 2 == 1 else 'PAIR'
            pbt_outcome = hands_data[hand_number - 1].pbt_result

            impair_pair_analysis['sequence_positions'].append(hand_number)
            impair_pair_analysis['position_types'].append(position_type)
            impair_pair_analysis['pbt_outcomes'].append(pbt_outcome)

        # Analyse corrélations IMPAIR/PAIR → P/B/T
        impair_outcomes = [outcome for i, outcome in enumerate(impair_pair_analysis['pbt_outcomes'])
                          if impair_pair_analysis['position_types'][i] == 'IMPAIR']
        pair_outcomes = [outcome for i, outcome in enumerate(impair_pair_analysis['pbt_outcomes'])
                        if impair_pair_analysis['position_types'][i] == 'PAIR']

        impair_pair_analysis['correlations'] = {
            'impair_to_player': impair_outcomes.count('P') / max(1, len(impair_outcomes)),
            'impair_to_banker': impair_outcomes.count('B') / max(1, len(impair_outcomes)),
            'impair_to_tie': impair_outcomes.count('T') / max(1, len(impair_outcomes)),
            'pair_to_player': pair_outcomes.count('P') / max(1, len(pair_outcomes)),
            'pair_to_banker': pair_outcomes.count('B') / max(1, len(pair_outcomes)),
            'pair_to_tie': pair_outcomes.count('T') / max(1, len(pair_outcomes)),
            'total_impair_hands': len(impair_outcomes),
            'total_pair_hands': len(pair_outcomes)
        }

        # Analyse séquences consécutives
        impair_consecutive = self._count_consecutive_pattern(impair_pair_analysis['position_types'], 'IMPAIR')
        pair_consecutive = self._count_consecutive_pattern(impair_pair_analysis['position_types'], 'PAIR')

        impair_pair_analysis['consecutive_patterns'] = {
            'max_impair_consecutive': impair_consecutive,
            'max_pair_consecutive': pair_consecutive,
            'impair_alert_level': 2 if impair_consecutive >= self.config.impair_alert_threshold_high else (1 if impair_consecutive >= self.config.impair_alert_threshold_medium else 0),
            'pair_alert_level': 2 if pair_consecutive >= self.config.pair_alert_threshold_high else (1 if pair_consecutive >= self.config.pair_alert_threshold_medium else 0)
        }

        return impair_pair_analysis

    def _analyze_complete_desync_sync_index(self, hands_data: List) -> Dict:
        """
        Analyse complète INDEX 2 : DESYNC/SYNC

        Calcule la synchronisation entre position attendue et résultat réel
        """
        desync_sync_analysis = {
            'sync_sequence': [],           # ['SYNC','DESYNC','SYNC',...]
            'sync_strength': [],           # [0.8, 0.3, 0.9, ...]
            'desync_periods': [],          # Périodes de désynchronisation
            'sync_recovery_points': [],    # Points de re-synchronisation
            'global_sync_rate': 0.0,
            'sync_patterns': {}
        }

        # Pattern attendu de base (alternance P/B)
        expected_pattern = ['P', 'B']

        # Calcul synchronisation pour chaque manche
        for i, hand in enumerate(hands_data):
            hand_number = i + 1
            actual_outcome = hand.pbt_result
            expected = expected_pattern[i % len(expected_pattern)]

            # Mesure synchronisation
            if actual_outcome == expected:
                sync_status = 'SYNC'
                sync_strength = 1.0
            else:
                sync_status = 'DESYNC'
                # Force désynchronisation basée sur écart
                if actual_outcome == 'T':  # Tie = forte désynchronisation
                    sync_strength = 0.1
                else:
                    sync_strength = 0.3

            desync_sync_analysis['sync_sequence'].append(sync_status)
            desync_sync_analysis['sync_strength'].append(sync_strength)

        # Calcul taux synchronisation global
        sync_count = desync_sync_analysis['sync_sequence'].count('SYNC')
        desync_sync_analysis['global_sync_rate'] = sync_count / len(hands_data)

        # Identification périodes de désynchronisation
        desync_sync_analysis['desync_periods'] = self._identify_desync_periods(
            desync_sync_analysis['sync_sequence']
        )

        return desync_sync_analysis

    def _analyze_complete_combined_index(self, impair_pair_data: Dict, desync_sync_data: Dict, hands_data: List) -> Dict:
        """
        Analyse complète INDEX 3 : COMBINÉ

        Combine les indices IMPAIR/PAIR et DESYNC/SYNC
        """
        combined_analysis = {
            'combined_sequence': [],       # ['IMPAIR_SYNC','PAIR_DESYNC',...]
            'pattern_frequencies': {},     # Fréquences des combinaisons
            'transition_matrix': {},       # Matrice transitions entre états
            'dominant_patterns': [],       # Patterns les plus fréquents
            'correlation_strength': 0.0
        }

        # Création séquence combinée
        for i in range(len(impair_pair_data['position_types'])):
            position_type = impair_pair_data['position_types'][i]
            sync_status = desync_sync_data['sync_sequence'][i]

            combined_state = f"{position_type}_{sync_status}"
            combined_analysis['combined_sequence'].append(combined_state)

        # Calcul fréquences
        unique_states = set(combined_analysis['combined_sequence'])
        for state in unique_states:
            count = combined_analysis['combined_sequence'].count(state)
            combined_analysis['pattern_frequencies'][state] = count / len(combined_analysis['combined_sequence'])

        # Identification patterns dominants
        combined_analysis['dominant_patterns'] = sorted(
            combined_analysis['pattern_frequencies'].items(),
            key=lambda x: x[1],
            reverse=True
        )[:3]  # Top 3

        return combined_analysis

    def _analyze_complete_pbt_index(self, hands_data: List) -> Dict:
        """
        Analyse complète INDEX 4 : P/B/T

        Analyse détaillée des résultats Player/Banker/Tie
        """
        pbt_analysis = {
            'pbt_sequence': [],            # ['P','B','P','T','B',...]
            'global_frequencies': {},      # Fréquences P/B/T globales
            'consecutive_sequences': {},   # Séquences consécutives
            'distribution_patterns': {},   # Patterns de distribution
            'tie_clustering': {},          # Analyse clustering des Ties
            'alternation_patterns': {}     # Patterns d'alternance
        }

        # Extraction séquence P/B/T complète
        pbt_sequence = [hand.pbt_result for hand in hands_data]
        pbt_analysis['pbt_sequence'] = pbt_sequence

        # Fréquences globales
        total_hands = len(pbt_sequence)
        pbt_analysis['global_frequencies'] = {
            'P': pbt_sequence.count('P') / total_hands,
            'B': pbt_sequence.count('B') / total_hands,
            'T': pbt_sequence.count('T') / total_hands,
            'total_hands': total_hands
        }

        # Analyse séquences consécutives pour chaque outcome
        for outcome in ['P', 'B', 'T']:
            consecutive_sequences = self._find_consecutive_sequences(pbt_sequence, outcome)
            pbt_analysis['consecutive_sequences'][outcome] = {
                'max_length': max(consecutive_sequences) if consecutive_sequences else 0,
                'average_length': sum(consecutive_sequences) / len(consecutive_sequences) if consecutive_sequences else 0,
                'frequency': len(consecutive_sequences),
                'sequences': consecutive_sequences
            }

        # Analyse clustering des Ties
        tie_positions = [i for i, outcome in enumerate(pbt_sequence) if outcome == 'T']
        pbt_analysis['tie_clustering'] = {
            'tie_positions': tie_positions,
            'tie_count': len(tie_positions),
            'tie_density': len(tie_positions) / total_hands if total_hands > 0 else 0
        }

        return pbt_analysis

    def _analyze_complete_so_index(self, hands_data: List) -> Dict:
        """
        Analyse complète INDEX 5 : S/O (SAME/OPPOSITE)

        Analyse les patterns de répétition vs changement
        """
        so_analysis = {
            'so_sequence': [],             # ['S','O','S','O',...]
            'so_frequencies': {},          # Fréquences S vs O
            'so_consecutive': {},          # Séquences consécutives S ou O
            'so_patterns': {},             # Patterns récurrents
            'so_correlation_pbt': {},      # Corrélation S/O avec P/B/T
            'so_transitions': {}           # Analyse transitions S→O, O→S
        }

        # Calcul séquence S/O (commence à la manche 2)
        pbt_sequence = [hand.pbt_result for hand in hands_data]

        for i in range(1, len(pbt_sequence)):
            current_outcome = pbt_sequence[i]
            previous_outcome = pbt_sequence[i-1]

            if current_outcome == previous_outcome:
                so_status = 'S'  # Same
            else:
                so_status = 'O'  # Opposite

            so_analysis['so_sequence'].append(so_status)

        # Fréquences S/O
        if so_analysis['so_sequence']:
            total_so = len(so_analysis['so_sequence'])
            so_analysis['so_frequencies'] = {
                'S': so_analysis['so_sequence'].count('S') / total_so,
                'O': so_analysis['so_sequence'].count('O') / total_so,
                'total_so_hands': total_so
            }

            # Séquences consécutives
            for so_type in ['S', 'O']:
                consecutive = self._find_consecutive_sequences(so_analysis['so_sequence'], so_type)
                so_analysis['so_consecutive'][so_type] = {
                    'max_length': max(consecutive) if consecutive else 0,
                    'average_length': sum(consecutive) / len(consecutive) if consecutive else 0,
                    'frequency': len(consecutive)
                }

        return so_analysis

    def _synthesize_complete_analysis(self, all_indices: Dict) -> Dict:
        """
        Synthèse finale de l'analyse complète des 5 indices
        """
        synthesis = {
            'analysis_quality': 0.0,
            'dominant_correlations': [],
            'high_confidence_zones': [],
            'prediction_signals': {},
            'risk_assessment': {},
            'sequence_insights': {}
        }

        # Calcul qualité analyse globale
        quality_factors = []

        # Facteur 1 : Richesse des données
        total_hands = len(all_indices['pbt']['pbt_sequence'])
        data_richness = min(1.0, total_hands / 50.0)  # Optimal à 50+ mains
        quality_factors.append(data_richness)

        # Facteur 2 : Cohérence des patterns
        impair_pair_coherence = 1.0 - abs(0.5 - all_indices['impair_pair']['correlations'].get('impair_to_player', 0.5))
        quality_factors.append(impair_pair_coherence)

        # Facteur 3 : Stabilité synchronisation
        sync_stability = all_indices['desync_sync']['global_sync_rate']
        quality_factors.append(sync_stability)

        synthesis['analysis_quality'] = sum(quality_factors) / len(quality_factors)

        # Identification corrélations dominantes
        synthesis['dominant_correlations'] = self._identify_dominant_correlations(all_indices)

        # Zones haute confiance
        synthesis['high_confidence_zones'] = self._identify_high_confidence_zones(all_indices)

        return synthesis

    def _identify_desync_periods(self, sync_sequence: List[str]) -> List[Dict]:
        """Identifie les périodes de désynchronisation"""
        periods = []
        current_period = None

        for i, status in enumerate(sync_sequence):
            if status == 'DESYNC':
                if current_period is None:
                    current_period = {'start': i, 'length': 1}
                else:
                    current_period['length'] += 1
            else:
                if current_period is not None:
                    current_period['end'] = i - 1
                    periods.append(current_period)
                    current_period = None

        # Fermer la dernière période si nécessaire
        if current_period is not None:
            current_period['end'] = len(sync_sequence) - 1
            periods.append(current_period)

        return periods

    def _find_consecutive_sequences(self, sequence: List[str], pattern: str) -> List[int]:
        """Trouve toutes les séquences consécutives d'un pattern"""
        consecutive_lengths = []
        current_length = 0

        for item in sequence:
            if item == pattern:
                current_length += 1
            else:
                if current_length > 0:
                    consecutive_lengths.append(current_length)
                    current_length = 0

        # Ajouter la dernière séquence si elle se termine par le pattern
        if current_length > 0:
            consecutive_lengths.append(current_length)

        return consecutive_lengths

    def _identify_dominant_correlations(self, all_indices: Dict) -> List[Dict]:
        """Identifie les corrélations dominantes entre indices"""
        correlations = []

        # Corrélation IMPAIR/PAIR → P/B/T
        impair_pair_corr = all_indices['impair_pair']['correlations']
        max_impair_corr = max(impair_pair_corr['impair_to_player'], impair_pair_corr['impair_to_banker'])
        max_pair_corr = max(impair_pair_corr['pair_to_player'], impair_pair_corr['pair_to_banker'])

        if max_impair_corr > 0.6:
            correlations.append({
                'type': 'impair_pbt',
                'strength': max_impair_corr,
                'pattern': 'IMPAIR→P' if impair_pair_corr['impair_to_player'] > impair_pair_corr['impair_to_banker'] else 'IMPAIR→B'
            })

        if max_pair_corr > 0.6:
            correlations.append({
                'type': 'pair_pbt',
                'strength': max_pair_corr,
                'pattern': 'PAIR→P' if impair_pair_corr['pair_to_player'] > impair_pair_corr['pair_to_banker'] else 'PAIR→B'
            })

        return correlations

    def _identify_high_confidence_zones(self, all_indices: Dict) -> List[Dict]:
        """Identifie les zones de haute confiance"""
        zones = []

        # Zone basée sur synchronisation élevée
        sync_rate = all_indices['desync_sync']['global_sync_rate']
        if sync_rate > 0.7:
            zones.append({
                'type': 'high_sync',
                'confidence': sync_rate,
                'description': f'Synchronisation élevée ({sync_rate:.1%})'
            })

        # Zone basée sur patterns combinés dominants
        combined_patterns = all_indices['combined']['dominant_patterns']
        if combined_patterns and combined_patterns[0][1] > 0.4:  # Plus de 40% de fréquence
            zones.append({
                'type': 'dominant_combined',
                'confidence': combined_patterns[0][1],
                'pattern': combined_patterns[0][0],
                'description': f'Pattern combiné dominant: {combined_patterns[0][0]}'
            })

        return zones

    # ========================================================================
    # MÉTHODES UTILITAIRES CLUSTER AZR
    # ========================================================================

    def _update_performance_metrics(self, prediction: Dict, total_time: float):
        """Mise à jour métriques performance du cluster"""
        self.performance_metrics['total_predictions'] += 1

        if prediction and prediction.get('cluster_confidence', 0) > 0.5:
            self.performance_metrics['successful_predictions'] += 1

        # Calcul moyenne confiance
        current_avg = self.performance_metrics['average_confidence']
        total_preds = self.performance_metrics['total_predictions']
        new_confidence = prediction.get('cluster_confidence', 0) if prediction else 0

        self.performance_metrics['average_confidence'] = (
            (current_avg * (total_preds - 1) + new_confidence) / total_preds
        )

        # Efficacité timing (170ms = 100%)
        self.performance_metrics['timing_efficiency'] = min(self.config.cluster_total_time_ms / total_time, 1.0)

    def _count_consecutive_pattern(self, sequence: List[str], pattern: str) -> int:
        """Compte séquences consécutives d'un pattern donné"""
        if not sequence:
            return 0

        max_consecutive = 0
        current_consecutive = 0

        for item in sequence:
            if item == pattern:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_rupture_probability(self, impair_count: int, pair_count: int) -> float:
        """Calcule probabilité de rupture basée sur séquences consécutives"""
        # Plus la séquence est longue, plus la probabilité de rupture augmente
        if impair_count >= self.config.impair_alert_threshold_high:
            return min(self.config.rupture_probability_impair_base + (impair_count - self.config.impair_alert_threshold_high) * self.config.rupture_probability_increment, self.config.rupture_probability_max)
        elif pair_count >= self.config.pair_alert_threshold_high:
            return min(self.config.rupture_probability_pair_base + (pair_count - self.config.pair_alert_threshold_high) * self.config.rupture_probability_increment, self.config.rupture_probability_max)
        else:
            return self.config.rupture_probability_base  # Probabilité neutre

    def _analyze_correlations_std_dev(self, pair_impair_seq: List[str],
                                    sync_desync_seq: List[str],
                                    combined_seq: List[str],
                                    so_seq: List[str]) -> Dict:
        """Analyse corrélations avec approche anti-moyennes (écart-type)"""
        import statistics

        correlations = {}

        # Analyse corrélations PAIR/IMPAIR → S/O
        if len(so_seq) > 2:
            pair_s_count = sum(1 for p, s in zip(pair_impair_seq[:len(so_seq)], so_seq) if p == 'PAIR' and s == 'S')
            impair_s_count = sum(1 for p, s in zip(pair_impair_seq[:len(so_seq)], so_seq) if p == 'IMPAIR' and s == 'S')

            correlations['pair_impair'] = {
                'pair_s_ratio': pair_s_count / max(pair_impair_seq[:len(so_seq)].count('PAIR'), 1),
                'impair_s_ratio': impair_s_count / max(pair_impair_seq[:len(so_seq)].count('IMPAIR'), 1),
                'std_dev': statistics.stdev([pair_s_count, impair_s_count]) if len(so_seq) > 1 else 1.0
            }

        # Analyse corrélations index combiné → S/O (priorité découvertes)
        if len(so_seq) > 2 and len(combined_seq) >= len(so_seq):
            impair_sync_s = sum(1 for c, s in zip(combined_seq[:len(so_seq)], so_seq) if c == 'IMPAIR_SYNC' and s == 'S')
            pair_sync_o = sum(1 for c, s in zip(combined_seq[:len(so_seq)], so_seq) if c == 'PAIR_SYNC' and s == 'O')

            correlations['combined'] = {
                'impair_sync_s_ratio': impair_sync_s / max(combined_seq[:len(so_seq)].count('IMPAIR_SYNC'), 1),
                'pair_sync_o_ratio': pair_sync_o / max(combined_seq[:len(so_seq)].count('PAIR_SYNC'), 1),
                'std_dev': statistics.stdev([impair_sync_s, pair_sync_o]) if len(so_seq) > 1 else 1.0,
                'dominant_pattern': 'IMPAIR_SYNC→S' if impair_sync_s > pair_sync_o else 'PAIR_SYNC→O'
            }

        return correlations

    def _identify_improbability_zones(self, impair_count: int, pair_count: int,
                                    impair_alert: int, pair_alert: int,
                                    correlations: Dict) -> Dict:
        """Identification zones d'improbabilité pour rollout générateur"""
        zones = {
            'avoid_zones': [],
            'caution_zones': [],
            'opportunity_zones': [],
            'high_confidence_zones': []
        }

        # Zones basées sur séquences consécutives
        if impair_alert >= 2:  # Alerte critique IMPAIR
            zones['avoid_zones'].append({
                'type': 'impair_continuation',
                'reason': 'Ultra-rare territory (607 pure games only)',
                'recommendation': 'Force rupture toward PAIR'
            })
            zones['opportunity_zones'].append({
                'type': 'impair_to_pair_transition',
                'confidence': 0.85,
                'reason': 'High rupture probability'
            })

        # Zones basées sur corrélations fiables (faible écart-type)
        for index_type, correlation_data in correlations.items():
            if correlation_data.get('std_dev', 1.0) < self.config.correlation_reliability_threshold:  # Seuil fiabilité
                zones['high_confidence_zones'].append({
                    'type': f'{index_type}_correlation',
                    'pattern': correlation_data.get('dominant_pattern'),
                    'confidence': 1.0 - correlation_data['std_dev']
                })

        return zones

    def _define_generation_space(self, consecutive_analysis: Dict, zones: Dict, correlations: Dict) -> Dict:
        """Définition espace de génération pour rollout générateur"""
        generation_space = {
            'sequence_length': self.config.default_sequence_length,  # Longueur par défaut
            'forbidden_patterns': [],
            'preferred_correlations': [],
            'transition_opportunities': []
        }

        # Ajustement longueur selon contexte
        if consecutive_analysis.get('impair_alert_level', 0) >= 1:
            generation_space['sequence_length'] = self.config.extended_sequence_length  # Plus long si transitions détectées

        # Exclusion patterns improbables
        avoid_zones = zones.get('avoid_zones', [])
        for zone in avoid_zones:
            if zone['type'] == 'impair_continuation':
                generation_space['forbidden_patterns'].append('IMPAIR_consecutive')

        # Exploitation corrélations fiables
        for index_type, correlation_data in correlations.items():
            if correlation_data.get('std_dev', 1.0) < self.config.correlation_exploitation_threshold:  # Seuil exploitation
                generation_space['preferred_correlations'].append({
                    'index_type': index_type,
                    'pattern': correlation_data.get('dominant_pattern'),
                    'confidence': 1.0 - correlation_data['std_dev']
                })

        return generation_space

    def _generate_max_probability_sequence(self, generation_space: Dict) -> List[Dict]:
        """Génère séquence probabilité maximale basée sur corrélations fiables"""
        sequence_length = generation_space['sequence_length']
        sequence = []

        for i in range(sequence_length):
            # Logique basée sur corrélations préférées
            preferred_correlations = generation_space.get('preferred_correlations', [])

            if preferred_correlations:
                # Utiliser la corrélation la plus fiable
                best_correlation = max(preferred_correlations, key=lambda x: x.get('confidence', 0))

                if best_correlation.get('pattern') == 'IMPAIR_SYNC→S':
                    hand = {
                        'parity': 'IMPAIR',
                        'sync_state': 'SYNC',
                        'so_conversion': 'S',
                        'confidence': best_correlation.get('confidence', 0.8)
                    }
                else:
                    hand = {
                        'parity': 'PAIR',
                        'sync_state': 'SYNC',
                        'so_conversion': 'O',
                        'confidence': best_correlation.get('confidence', 0.7)
                    }
            else:
                # Séquence par défaut équilibrée
                hand = {
                    'parity': 'PAIR' if i % 2 == 0 else 'IMPAIR',
                    'sync_state': 'SYNC',
                    'so_conversion': 'S' if i % 2 == 0 else 'O',
                    'confidence': 0.6
                }

            sequence.append(hand)

        return sequence

    def _generate_alternative_sequence(self, generation_space: Dict) -> List[Dict]:
        """Génère séquence alternative crédible"""
        sequence_length = generation_space['sequence_length']
        sequence = []

        for i in range(sequence_length):
            # Alternative avec variation
            hand = {
                'parity': 'IMPAIR' if i % 2 == 0 else 'PAIR',
                'sync_state': 'DESYNC' if i % 3 == 0 else 'SYNC',
                'so_conversion': 'O' if i % 2 == 0 else 'S',
                'confidence': 0.65
            }
            sequence.append(hand)

        return sequence

    def _generate_rupture_sequence(self, generation_space: Dict) -> List[Dict]:
        """Génère séquence scénario rupture"""
        sequence_length = generation_space['sequence_length']
        sequence = []

        # Forcer rupture si patterns interdits détectés
        forbidden_patterns = generation_space.get('forbidden_patterns', [])
        force_pair = 'IMPAIR_consecutive' in forbidden_patterns

        for i in range(sequence_length):
            hand = {
                'parity': 'PAIR' if force_pair else ('IMPAIR' if i < 2 else 'PAIR'),
                'sync_state': 'SYNC',
                'so_conversion': 'O' if force_pair else 'S',
                'confidence': 0.7
            }
            sequence.append(hand)

        return sequence

    def _generate_conservative_sequence(self, generation_space: Dict) -> List[Dict]:
        """Génère séquence conservatrice équilibrée"""
        sequence_length = generation_space['sequence_length']
        sequence = []

        for i in range(sequence_length):
            # Séquence équilibrée conservatrice
            hand = {
                'parity': 'PAIR',
                'sync_state': 'SYNC',
                'so_conversion': 'S',
                'confidence': 0.5
            }
            sequence.append(hand)

        return sequence

    def _enrich_sequences_with_indexes(self, candidates: List[Dict]) -> List[Dict]:
        """Enrichissement séquences avec tous les index"""
        enriched = []

        for candidate in candidates:
            sequence_data = candidate['sequence_data']

            # Enrichissement pour chaque manche
            enriched_hands = []
            for hand_data in sequence_data:
                enriched_hand = {
                    # Index de base
                    'parity': hand_data.get('parity', 'PAIR'),
                    'sync_state': hand_data.get('sync_state', 'SYNC'),
                    'combined_state': f"{hand_data.get('parity', 'PAIR')}_{hand_data.get('sync_state', 'SYNC')}",

                    # Résultats générés
                    'pbt_result': 'PLAYER',  # Simplifié pour l'instant
                    'so_conversion': hand_data.get('so_conversion', 'S'),

                    # Métadonnées
                    'cards_used': 4,  # Minimum baccarat
                    'generation_confidence': hand_data.get('confidence', 0.7)
                }
                enriched_hands.append(enriched_hand)

            # Mise à jour candidat
            candidate['enriched_sequence'] = enriched_hands
            enriched.append(candidate)

        return enriched

    def _evaluate_sequence_quality(self, sequence: Dict, analyzer_report: Dict) -> Dict:
        """Évaluation qualité séquence pour rollout prédicteur"""
        evaluation = {
            'so_quality_score': 0.0,
            'analyzer_coherence_score': 0.0,
            'total_score': 0.0
        }

        # Évaluation qualité S/O (priorité absolue)
        so_sequence = [hand.get('so_conversion', 'S') for hand in sequence.get('enriched_sequence', [])]

        # Diversité S/O
        if len(set(so_sequence)) > 1:
            evaluation['so_quality_score'] += 0.3

        # Cohérence avec corrélations découvertes
        correlations = analyzer_report.get('correlation_analysis', {})
        combined_correlation = correlations.get('combined', {})

        if combined_correlation.get('std_dev', 1.0) < 0.2:  # Corrélation fiable
            evaluation['so_quality_score'] += 0.4

        # Cohérence avec analyseur
        consecutive_analysis = analyzer_report.get('consecutive_analysis', {})
        if consecutive_analysis.get('impair_alert_level', 0) >= 1:
            # Vérifier si séquence évite continuation IMPAIR
            if not any(hand.get('parity') == 'IMPAIR' for hand in sequence.get('enriched_sequence', [])):
                evaluation['analyzer_coherence_score'] += 0.5

        # Score total pondéré
        evaluation['total_score'] = (
            evaluation['so_quality_score'] * 0.7 +  # 70% priorité S/O
            evaluation['analyzer_coherence_score'] * 0.3  # 30% cohérence analyseur
        )

        return evaluation

    def _select_best_sequence(self, evaluated_sequences: List[Dict]) -> Dict:
        """Sélection meilleure séquence basée sur évaluations"""
        if not evaluated_sequences:
            return {}

        # Tri par score total décroissant
        sorted_sequences = sorted(
            evaluated_sequences,
            key=lambda x: x.get('evaluation', {}).get('total_score', 0),
            reverse=True
        )

        return sorted_sequences[0]

    def _calculate_cluster_confidence(self, best_sequence: Dict, analyzer_report: Dict) -> float:
        """Calcul confiance finale du cluster"""
        base_confidence = best_sequence.get('estimated_probability', 0.5)
        evaluation_score = best_sequence.get('evaluation', {}).get('total_score', 0.5)

        # Bonus si corrélations fiables détectées
        correlations = analyzer_report.get('correlation_analysis', {})
        correlation_bonus = 0.0

        for correlation_data in correlations.values():
            if correlation_data.get('std_dev', 1.0) < 0.15:
                correlation_bonus += 0.1

        # Confiance finale
        final_confidence = min(
            (base_confidence + evaluation_score + correlation_bonus) / 3.0,
            0.95  # Plafonné à 95%
        )

        return final_confidence

    def _extract_next_hand_prediction(self, best_sequence: Dict) -> Dict:
        """Extraction prédiction prochaine manche"""
        enriched_sequence = best_sequence.get('enriched_sequence', [])

        if enriched_sequence:
            next_hand = enriched_sequence[0]  # Premier élément de la séquence
            return {
                'predicted_so': next_hand.get('so_conversion', 'S'),
                'predicted_parity': next_hand.get('parity', 'PAIR'),
                'predicted_sync': next_hand.get('sync_state', 'SYNC'),
                'predicted_combined': next_hand.get('combined_state', 'PAIR_SYNC'),
                'confidence': next_hand.get('generation_confidence', 0.7)
            }

        return {
            'predicted_so': 'S',
            'predicted_parity': 'PAIR',
            'predicted_sync': 'SYNC',
            'predicted_combined': 'PAIR_SYNC',
            'confidence': 0.5
        }

# ============================================================================
# 🎯 SYSTÈME MASTER AZR - COORDINATION CLUSTERS
# ============================================================================

class AZRMaster:
    """
    Système Master AZR - Coordination des 8 clusters distribués

    Implémente l'architecture Master-Worker avec communication optimisée
    et synchronisation par barrières entre phases.
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.clusters = []
        self.num_clusters = self.config.cpu_cores  # 1 par cœur CPU

        # Initialisation des clusters
        for cluster_id in range(self.num_clusters):
            cluster = AZRCluster(cluster_id, config)
            self.clusters.append(cluster)

        # Communication inter-clusters (message passing)
        self.global_shared_memory = {
            'cluster_results': [],
            'consensus_prediction': None,
            'global_confidence': 0.0,
            'timing_metrics': {}
        }

        # Métriques système global
        self.system_metrics = {
            'total_predictions': 0,
            'consensus_achieved': 0,
            'average_cluster_agreement': 0.0,
            'system_efficiency': 0.0
        }

    def predict_next_sequence(self, standardized_sequence: Dict) -> Dict:
        """
        Prédiction principale utilisant tous les clusters en parallèle

        Args:
            standardized_sequence: Séquence complète depuis brûlage

        Returns:
            Dict: Prédiction consensus avec confiance système
        """
        import time
        import concurrent.futures

        start_time = time.time()

        try:
            # Phase parallèle : Exécution simultanée des 8 clusters
            cluster_results = []

            with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
                # Lancement parallèle des clusters
                future_to_cluster = {
                    executor.submit(cluster.execute_cluster_pipeline, standardized_sequence): cluster_id
                    for cluster_id, cluster in enumerate(self.clusters)
                }

                # Collecte des résultats
                for future in concurrent.futures.as_completed(future_to_cluster):
                    cluster_id = future_to_cluster[future]
                    try:
                        result = future.result(timeout=0.2)  # 200ms timeout par cluster
                        result['cluster_id'] = cluster_id
                        cluster_results.append(result)
                    except Exception as e:
                        logger.error(f"Erreur cluster {cluster_id}: {e}")
                        cluster_results.append({
                            'cluster_id': cluster_id,
                            'prediction': None,
                            'confidence': 0.0,
                            'error': str(e)
                        })

            # Phase consensus : Agrégation intelligente des résultats
            consensus_result = self._build_consensus(cluster_results)

            # Calcul timing total
            total_time = (time.time() - start_time) * 1000

            # Mise à jour métriques système
            self._update_system_metrics(consensus_result, cluster_results, total_time)

            # Résultat final système
            system_result = {
                'consensus_prediction': consensus_result,
                'cluster_results': cluster_results,
                'system_confidence': self.global_shared_memory['global_confidence'],
                'timing_metrics': {
                    'total_system_time_ms': total_time,
                    'clusters_completed': len([r for r in cluster_results if r.get('prediction')]),
                    'average_cluster_time_ms': sum(r.get('timing_breakdown', {}).get('total_ms', 0) for r in cluster_results) / len(cluster_results)
                },
                'system_metrics': self.system_metrics.copy()
            }

            return system_result

        except Exception as e:
            logger.error(f"Erreur système AZR Master: {e}")
            return {
                'consensus_prediction': None,
                'system_confidence': 0.0,
                'error': str(e)
            }

    def _build_consensus(self, cluster_results: List[Dict]) -> Dict:
        """
        Construction consensus intelligent à partir des résultats clusters

        Utilise vote pondéré par confiance et détection accord majoritaire.
        """
        valid_results = [r for r in cluster_results if r.get('prediction')]

        if not valid_results:
            return {
                'consensus_so': 'S',
                'consensus_confidence': 0.0,
                'agreement_level': 0.0,
                'voting_details': {'error': 'Aucun résultat valide'}
            }

        # Collecte prédictions S/O avec pondération confiance
        so_votes = {'S': 0.0, 'O': 0.0}
        total_weight = 0.0

        for result in valid_results:
            prediction = result.get('prediction', {})
            next_hand = prediction.get('next_hand_prediction', {})
            predicted_so = next_hand.get('predicted_so', 'S')
            confidence = prediction.get('cluster_confidence', 0.5)

            so_votes[predicted_so] += confidence
            total_weight += confidence

        # Normalisation votes
        if total_weight > 0:
            so_votes['S'] /= total_weight
            so_votes['O'] /= total_weight

        # Détermination consensus
        consensus_so = 'S' if so_votes['S'] > so_votes['O'] else 'O'
        consensus_confidence = max(so_votes['S'], so_votes['O'])

        # Calcul niveau d'accord
        agreement_level = abs(so_votes['S'] - so_votes['O'])  # Plus proche de 1 = plus d'accord

        # Bonus si 8 clusters d'accord (confiance boostée)
        if len(valid_results) == 8:
            unanimous_so = all(
                r.get('prediction', {}).get('next_hand_prediction', {}).get('predicted_so', 'S') == consensus_so
                for r in valid_results
            )
            if unanimous_so:
                consensus_confidence = min(consensus_confidence * 1.2, 0.95)  # Boost 20%
                agreement_level = 1.0

        # Stockage confiance globale
        self.global_shared_memory['global_confidence'] = consensus_confidence

        return {
            'consensus_so': consensus_so,
            'consensus_confidence': consensus_confidence,
            'agreement_level': agreement_level,
            'voting_details': {
                'so_votes': so_votes,
                'clusters_participating': len(valid_results),
                'total_clusters': len(cluster_results),
                'unanimous_agreement': agreement_level >= 0.8
            }
        }

    def _update_system_metrics(self, consensus_result: Dict, cluster_results: List[Dict], total_time: float):
        """Mise à jour métriques système global"""
        self.system_metrics['total_predictions'] += 1

        # Consensus atteint si accord > 60%
        if consensus_result.get('agreement_level', 0) > 0.6:
            self.system_metrics['consensus_achieved'] += 1

        # Calcul accord moyen clusters
        valid_results = [r for r in cluster_results if r.get('prediction')]
        if valid_results:
            cluster_confidences = [
                r.get('prediction', {}).get('cluster_confidence', 0.5)
                for r in valid_results
            ]
            self.system_metrics['average_cluster_agreement'] = sum(cluster_confidences) / len(cluster_confidences)

        # Efficacité système (170ms = 100%)
        self.system_metrics['system_efficiency'] = min(170.0 / total_time, 1.0)

    def get_system_status(self) -> Dict:
        """Retourne statut complet du système AZR"""
        cluster_status = []
        for i, cluster in enumerate(self.clusters):
            cluster_status.append({
                'cluster_id': i,
                'performance_metrics': cluster.performance_metrics.copy(),
                'shared_memory_size': len(cluster.shared_memory),
                'status': 'active'
            })

        return {
            'system_metrics': self.system_metrics.copy(),
            'global_shared_memory': self.global_shared_memory.copy(),
            'cluster_status': cluster_status,
            'architecture': {
                'num_clusters': self.num_clusters,
                'rollouts_per_cluster': 3,
                'total_rollouts': self.num_clusters * 3,
                'communication_model': 'Master-Worker + Message Passing',
                'synchronization': 'Barriers between phases'
            }
        }

# ============================================================================
# 🎲 GÉNÉRATEUR BACCARAT INTÉGRÉ
# ============================================================================

class BaccaratGenerator:
    """Générateur de parties baccarat intégré au modèle AZR"""

    def __init__(self, config: Optional[AZRConfig] = None):
        self.config = config or AZRConfig()
        self.shoe = []
        self.position = 0
        self.burn_cards = []
        self.cut_card_position = self.config.cut_card_position

    def reset_shoe(self):
        """Crée un nouveau sabot selon la configuration"""
        self.shoe = []
        for _ in range(self.config.num_decks):
            for suit in self.config.card_suits:
                for value in self.config.card_values:
                    self.shoe.append(value)

        random.shuffle(self.shoe)
        self.position = 0
        self.burn_cards = []
        self._burn_cards()

    def _burn_cards(self):
        """Procédure de brûlage selon la configuration"""
        if self.position >= len(self.shoe):
            raise Exception("Sabot épuisé avant brûlage")

        # Tirer la première carte
        first_card = self.shoe[self.position]
        self.position += 1

        # Calculer sa valeur baccarat selon la configuration
        if first_card in self.config.face_cards:
            burn_value = 0
        elif first_card == 'A':
            burn_value = self.config.burn_value_ace
        else:
            burn_value = int(first_card)

        # Ajouter la première carte aux cartes brûlées
        self.burn_cards.append(first_card)

        # Brûler les cartes supplémentaires selon la configuration
        if burn_value == 0:
            additional_burns = self.config.burn_value_zero_additional
        else:
            additional_burns = burn_value

        for _ in range(additional_burns):
            if self.position >= len(self.shoe):
                break
            self.burn_cards.append(self.shoe[self.position])
            self.position += 1

        # Vérifier les limites de brûlage selon la configuration
        total_burned = len(self.burn_cards)
        if total_burned < self.config.min_burn_cards or total_burned > self.config.max_burn_cards:
            logger.warning(f"Brûlage inhabituel: {total_burned} cartes (limites: {self.config.min_burn_cards}-{self.config.max_burn_cards})")

    def _get_baccarat_value(self, card: str) -> int:
        """Retourne la valeur baccarat d'une carte selon la configuration"""
        if card in self.config.face_cards:
            return 0
        elif card == 'A':
            return self.config.burn_value_ace
        else:
            return int(card)

    def _draw_card(self) -> str:
        """Tire une carte du sabot"""
        if self.position >= self.cut_card_position:
            raise Exception("Cut card atteinte - Nouveau sabot nécessaire")

        if self.position >= len(self.shoe):
            raise Exception("Sabot épuisé")

        card = self.shoe[self.position]
        self.position += 1
        return card

    def play_hand(self) -> Dict[str, Any]:
        """Joue une manche complète selon les règles du baccarat"""
        # Distribution initiale
        player_cards = [self._draw_card(), self._draw_card()]
        banker_cards = [self._draw_card(), self._draw_card()]

        # Calcul des totaux
        player_total = sum(self._get_baccarat_value(card) for card in player_cards) % self.config.modulo_ten
        banker_total = sum(self._get_baccarat_value(card) for card in banker_cards) % self.config.modulo_ten

        # Vérifier les naturels selon la configuration
        if player_total >= self.config.natural_threshold or banker_total >= self.config.natural_threshold:
            # Naturel - pas de 3ème carte
            pass
        else:
            # Règles de la 3ème carte selon la configuration
            player_third_card = None

            # Player tire selon le seuil configuré
            if player_total <= self.config.player_draw_threshold:
                player_third_card = self._draw_card()
                player_cards.append(player_third_card)
                player_total = sum(self._get_baccarat_value(card) for card in player_cards) % self.config.modulo_ten

            # Règles Banker selon 3ème carte Player
            if player_third_card is None:
                # Player n'a pas tiré
                if banker_total <= self.config.player_draw_threshold:  # Même règle que Player si pas de 3ème carte
                    banker_cards.append(self._draw_card())
                    banker_total = sum(self._get_baccarat_value(card) for card in banker_cards) % self.config.modulo_ten
            else:
                # Player a tiré - règles complexes
                player_third_value = self._get_baccarat_value(player_third_card)

                should_banker_draw = False
                if banker_total <= self.config.two_value:
                    should_banker_draw = True
                elif banker_total == self.config.three_value and player_third_value != self.config.eight_value:
                    should_banker_draw = True
                elif banker_total == self.config.four_value and player_third_value in [self.config.two_value, self.config.three_value, self.config.four_value, self.config.five_value, self.config.six_value, self.config.seven_value]:
                    should_banker_draw = True
                elif banker_total == self.config.five_value and player_third_value in [self.config.four_value, self.config.five_value, self.config.six_value, self.config.seven_value]:
                    should_banker_draw = True
                elif banker_total == self.config.six_value and player_third_value in [self.config.six_value, self.config.seven_value]:
                    should_banker_draw = True

                if should_banker_draw:
                    banker_cards.append(self._draw_card())
                    banker_total = sum(self._get_baccarat_value(card) for card in banker_cards) % self.config.modulo_ten

        # Déterminer le résultat
        if player_total > banker_total:
            result = 'PLAYER'
        elif banker_total > player_total:
            result = 'BANKER'
        else:
            result = 'TIE'

        # Calculer la parité (nombre total de cartes utilisées)
        total_cards = len(player_cards) + len(banker_cards)
        parity = 'PAIR' if total_cards % self.config.two_value == self.config.zero_value else 'IMPAIR'

        return {
            'result': result,
            'parity': parity,
            'player_total': player_total,
            'banker_total': banker_total,
            'cards_used': total_cards
        }

    def generate_game_data(self, target_pb_hands: int = None) -> Dict[str, Any]:
        """
        Génère une partie complète jusqu'au cut card

        Args:
            target_pb_hands: Limite optionnelle (None = jouer jusqu'au cut card)
        """
        self.reset_shoe()

        hands = []
        pb_count = 0
        total_hands = 0

        # Données de brûlage
        burn_parity = 'PAIR' if len(self.burn_cards) % self.config.two_value == self.config.zero_value else 'IMPAIR'

        # Jouer jusqu'au cut card (ou limite optionnelle)
        while True:
            try:
                # Vérifier si on peut encore jouer (cut card)
                if self.position >= self.cut_card_position:
                    logger.info(f"Cut card atteint - Fin de partie après {pb_count} manches P/B")
                    break

                # Vérifier limite optionnelle si spécifiée
                if target_pb_hands is not None and pb_count >= target_pb_hands:
                    logger.info(f"Limite de {target_pb_hands} manches P/B atteinte")
                    break

                hand_data = self.play_hand()
                total_hands += 1

                # Compter les manches P/B
                if hand_data['result'] in ['PLAYER', 'BANKER']:
                    pb_count += 1
                    hand_data['pb_hand_number'] = pb_count
                else:
                    hand_data['pb_hand_number'] = pb_count  # TIE garde le même numéro P/B

                hands.append(hand_data)

            except Exception as e:
                logger.info(f"Fin de partie à la manche {total_hands + 1}: {e}")
                break

        # Calculer les états SYNC/DESYNC et conversions S/O
        hands = self._calculate_sync_and_so(hands, burn_parity)

        return {
            'burn_cards_count': len(self.burn_cards),
            'burn_parity': burn_parity,
            'total_hands': total_hands,
            'pb_hands': pb_count,
            'tie_hands': total_hands - pb_count,
            'hands': hands
        }

    def _calculate_sync_and_so(self, hands: List[Dict], burn_parity: str) -> List[Dict]:
        """Calcule les états SYNC/DESYNC et conversions S/O"""
        # État initial basé sur le brûlage
        current_sync = 'SYNC' if burn_parity == 'PAIR' else 'DESYNC'

        # Calculer pour chaque manche
        for i, hand in enumerate(hands):
            # CORRECTION: Mettre à jour l'état AVANT de l'assigner à la manche
            if hand['parity'] == 'IMPAIR':
                current_sync = 'DESYNC' if current_sync == 'SYNC' else 'SYNC'

            # Assigner l'état correct à cette manche
            hand['sync_state'] = current_sync

            # Calculer la conversion S/O pour les manches P/B
            if hand['result'] in ['PLAYER', 'BANKER'] and i > 0:
                # Trouver la manche P/B précédente
                prev_pb_result = None
                for j in range(i-1, -1, -1):
                    if hands[j]['result'] in ['PLAYER', 'BANKER']:
                        prev_pb_result = hands[j]['result']
                        break

                if prev_pb_result:
                    if hand['result'] == prev_pb_result:
                        hand['so_conversion'] = 'S'  # Same
                    else:
                        hand['so_conversion'] = 'O'  # Opposite
                else:
                    hand['so_conversion'] = '--'  # Première manche P/B
            else:
                hand['so_conversion'] = '--'  # TIE ou première manche

        return hands

# ============================================================================
# 📖 4. DATA LOADER INTÉGRÉ
# ============================================================================

class BaccaratDataLoader:
    """Chargeur de données baccarat intégré au modèle AZR"""

    def __init__(self, filename: str = None):
        self.filename = filename
        self.data = None
        self.games = []
        self.metadata = {}

        if filename:
            self._load_data()

    def _load_data(self):
        """Charge les données depuis le fichier JSON"""
        try:
            if not os.path.exists(self.filename):
                raise FileNotFoundError(f"Fichier non trouvé: {self.filename}")

            with open(self.filename, 'r', encoding='utf-8') as f:
                self.data = json.load(f)

            self.metadata = self.data.get('metadata', {})
            self.games = self.data.get('games', [])

            logger.info(f"✅ Données chargées: {len(self.games)} parties depuis {self.filename}")

        except Exception as e:
            raise Exception(f"Erreur lors du chargement: {e}")

    def load_from_data(self, data: Dict):
        """Charge les données depuis un dictionnaire"""
        self.data = data
        self.metadata = data.get('metadata', {})
        self.games = data.get('games', [])
        logger.info(f"✅ Données chargées: {len(self.games)} parties depuis dictionnaire")

    def get_game(self, game_number: int) -> Optional[Dict]:
        """Retourne une partie complète par son numéro"""
        for game in self.games:
            if game.get('game_number', 0) == game_number:
                return game.copy()
        return None

    def get_hands_iterator(self, game_number: int) -> Iterator[Dict]:
        """Retourne un itérateur pour parcourir les manches d'une partie"""
        game = self.get_game(game_number)
        if game and 'hands' in game:
            for hand in game['hands']:
                yield hand.copy()

    def get_total_games(self) -> int:
        """Retourne le nombre total de parties"""
        return len(self.games)

# ============================================================================
# 🎮 3. INTERFACE GRAPHIQUE ULTRA-SIMPLIFIÉE
# ============================================================================
#
# Interface 3 colonnes : Player (bleu) / Banker (rouge) / Tie (vert)
# - Initialisation cartes brûlées (coin haut gauche)
# - Compteur manches P/B (centré)
# - Affichage prédictions S/O en temps réel
# - Bouton Reset (coin bas droite)
# - Données générées silencieusement en arrière-plan
# ============================================================================

class AZRBaccaratInterface:
    """Interface graphique ultra-simplifiée avec prédictions AZR en temps réel"""

    def __init__(self, azr_predictor: 'AZRBaccaratPredictor' = None):
        # Modèle AZR - sera initialisé après la définition de la classe
        self.azr_predictor = azr_predictor
        # Configuration centralisée
        self.config = azr_predictor.config if azr_predictor else AZRConfig()

        # Interface graphique
        self.root = tk.Tk()
        self.root.title("🧠 Interface Baccarat AZR Intégrée")
        self.root.geometry("800x600")

        # Forcer l'affichage au premier plan
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        self.root.focus_force()

        # État de l'interface
        self.burn_initialized = False
        self.current_pb_number = self.config.one_value
        self.current_sync_state = 'SYNC'
        self.last_pb_result = None
        self.last_so_conversion = '--'

        # Données de la partie
        self.current_game = {
            'game_number': self.config.one_value,
            'initialization': {
                'burn_cards_count': self.config.zero_value,
                'burn_parity': 'PAIR',
                'initial_sync_state': 'SYNC'
            },
            'statistics': {
                'total_hands': self.config.zero_value,
                'pb_hands': self.config.zero_value,
                'tie_hands': self.config.zero_value,
                'so_conversions': self.config.zero_value
            },
            'hands': []
        }

        # Historique des prédictions pour affichage
        self.prediction_history = []

        self._create_interface()
        logger.info("🎮 Interface graphique AZR initialisée")

    def set_azr_predictor(self, azr_predictor: 'AZRBaccaratPredictor'):
        """Définit le prédicteur AZR à utiliser"""
        self.azr_predictor = azr_predictor

    # ------------------------------------------------------------------------
    # 🎨 3.1 CRÉATION DE L'INTERFACE PRINCIPALE
    # ------------------------------------------------------------------------

    def _create_interface(self):
        """Crée l'interface graphique ultra-simplifiée"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Initialisation des cartes brûlées (coin haut gauche)
        self._create_burn_section(main_frame)

        # Compteur de manches (centré en haut)
        self._create_counter_section(main_frame)

        # 3 colonnes principales (Player/Banker/Tie)
        self._create_columns_section(main_frame)

        # Bouton Reset (coin bas droite)
        self._create_reset_section(main_frame)

    # ------------------------------------------------------------------------
    # 🎨 3.2 SECTIONS DE L'INTERFACE
    # ------------------------------------------------------------------------

    def _create_burn_section(self, parent):
        """Crée la section d'initialisation des cartes brûlées (coin haut gauche)"""
        burn_frame = ttk.Frame(parent)
        burn_frame.place(x=0, y=0)

        ttk.Label(burn_frame, text="Cartes brûlées:", font=("Arial", 9)).pack()

        buttons_frame = ttk.Frame(burn_frame)
        buttons_frame.pack(pady=5)

        self.burn_pair_btn = ttk.Button(buttons_frame, text="PAIR", width=8,
                                       command=lambda: self.initialiser_burn('PAIR'))
        self.burn_pair_btn.pack(side=tk.LEFT, padx=2)

        self.burn_impair_btn = ttk.Button(buttons_frame, text="IMPAIR", width=8,
                                         command=lambda: self.initialiser_burn('IMPAIR'))
        self.burn_impair_btn.pack(side=tk.LEFT, padx=2)

    def _create_counter_section(self, parent):
        """Crée le compteur de manches et l'affichage des prédictions (centré en haut)"""
        counter_frame = ttk.Frame(parent)
        counter_frame.pack(pady=(50, 30))

        self.counter_label = ttk.Label(counter_frame, text="Manche P/B: 1",
                                      font=("Arial", 16, "bold"))
        self.counter_label.pack()

        # Affichage de la prédiction pour la manche suivante
        self.prediction_label = ttk.Label(counter_frame, text="Prédiction: Attendre",
                                         font=("Arial", 14, "bold"), foreground="blue")
        self.prediction_label.pack(pady=(10, 0))

    def _create_columns_section(self, parent):
        """Crée les 3 colonnes principales (Player/Banker/Tie)"""
        columns_frame = ttk.Frame(parent)
        columns_frame.pack(expand=True, fill=tk.BOTH, pady=20)

        # Configuration des colonnes
        columns_frame.grid_columnconfigure(0, weight=1)
        columns_frame.grid_columnconfigure(1, weight=1)
        columns_frame.grid_columnconfigure(2, weight=1)

        # Colonne 1: PLAYER (Bleu foncé)
        player_frame = ttk.LabelFrame(columns_frame, text="PLAYER", padding="20")
        player_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        player_pair_btn = tk.Button(player_frame, text="Player Pair", width=15, bg="#1E3A8A", fg="white",
                                   font=("Arial", 10, "bold"), relief="raised", bd=3,
                                   command=lambda: self.process_hand('PLAYER', 'PAIR'))
        player_pair_btn.pack(pady=10)

        player_impair_btn = tk.Button(player_frame, text="Player Impair", width=15, bg="#1E3A8A", fg="white",
                                     font=("Arial", 10, "bold"), relief="raised", bd=3,
                                     command=lambda: self.process_hand('PLAYER', 'IMPAIR'))
        player_impair_btn.pack(pady=10)

        # Colonne 2: BANKER (Rouge foncé)
        banker_frame = ttk.LabelFrame(columns_frame, text="BANKER", padding="20")
        banker_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")

        banker_pair_btn = tk.Button(banker_frame, text="Banker Pair", width=15, bg="#B91C1C", fg="white",
                                   font=("Arial", 10, "bold"), relief="raised", bd=3,
                                   command=lambda: self.process_hand('BANKER', 'PAIR'))
        banker_pair_btn.pack(pady=10)

        banker_impair_btn = tk.Button(banker_frame, text="Banker Impair", width=15, bg="#B91C1C", fg="white",
                                     font=("Arial", 10, "bold"), relief="raised", bd=3,
                                     command=lambda: self.process_hand('BANKER', 'IMPAIR'))
        banker_impair_btn.pack(pady=10)

        # Colonne 3: TIE (Vert foncé)
        tie_frame = ttk.LabelFrame(columns_frame, text="TIE", padding="20")
        tie_frame.grid(row=0, column=2, padx=10, pady=10, sticky="nsew")

        tie_pair_btn = tk.Button(tie_frame, text="Tie Pair", width=15, bg="#166534", fg="white",
                                font=("Arial", 10, "bold"), relief="raised", bd=3,
                                command=lambda: self.process_hand('TIE', 'PAIR'))
        tie_pair_btn.pack(pady=10)

        tie_impair_btn = tk.Button(tie_frame, text="Tie Impair", width=15, bg="#166534", fg="white",
                                  font=("Arial", 10, "bold"), relief="raised", bd=3,
                                  command=lambda: self.process_hand('TIE', 'IMPAIR'))
        tie_impair_btn.pack(pady=10)

    def _create_reset_section(self, parent):
        """Crée le bouton Reset (coin bas droite)"""
        reset_frame = ttk.Frame(parent)
        reset_frame.place(relx=1.0, rely=1.0, anchor="se")

        ttk.Button(reset_frame, text="🔄 Reset",
                  command=self.reset_partie).pack()

    # ------------------------------------------------------------------------
    # 🎯 3.3 TRAITEMENT DES DONNÉES DE JEUX
    # ------------------------------------------------------------------------

    def process_hand(self, result: str, parity: str):
        """Traite une manche complète avec résultat et parité"""
        if not self.burn_initialized:
            messagebox.showwarning("Attention", "Veuillez d'abord initialiser les cartes brûlées")
            return

        # Déterminer le numéro P/B
        if result in ['PLAYER', 'BANKER']:
            pb_number = self.current_pb_number
        else:  # TIE
            pb_number = self.current_pb_number  # TIE garde le même numéro

        # CORRECTION: Mettre à jour l'état de synchronisation AVANT de créer hand_data
        if parity == 'IMPAIR':
            self.current_sync_state = 'DESYNC' if self.current_sync_state == 'SYNC' else 'SYNC'

        # Calculer la conversion S/O
        so_conversion = '--'
        if result in ['PLAYER', 'BANKER']:
            if self.last_pb_result is not None:
                if result == self.last_pb_result:
                    so_conversion = 'S'
                else:
                    so_conversion = 'O'

            # Mettre à jour pour la prochaine manche
            self.last_pb_result = result
            self.last_so_conversion = so_conversion
            self.current_pb_number += self.config.one_value

        # Créer les données de la manche avec l'état correct
        hand_data = {
            'pb_hand_number': pb_number,
            'result': result,
            'parity': parity,
            'sync_state': self.current_sync_state,
            'so_conversion': so_conversion
        }

        # Ajouter à la partie
        self.current_game['hands'].append(hand_data)

        # Mettre à jour les statistiques
        self.current_game['statistics']['total_hands'] += self.config.one_value
        if result in ['PLAYER', 'BANKER']:
            self.current_game['statistics']['pb_hands'] += self.config.one_value
            if so_conversion != '--':
                self.current_game['statistics']['so_conversions'] += self.config.one_value
        else:
            self.current_game['statistics']['tie_hands'] += self.config.one_value

        # Envoyer au modèle AZR et obtenir la prédiction
        prediction = "Attendre"
        if self.azr_predictor:
            prediction = self.azr_predictor.receive_hand_data(hand_data)

        # Mettre à jour l'affichage du compteur et de la prédiction
        if result in ['PLAYER', 'BANKER']:
            self.counter_label.config(text=f"Manche P/B: {self.current_pb_number}")

        # Afficher la prédiction pour la manche suivante
        if prediction in ['S', 'O']:
            prediction_text = f"Prédiction: {prediction}"
            color = "green" if prediction == "S" else "red"
            self.prediction_label.config(text=prediction_text, foreground=color)
        else:
            self.prediction_label.config(text="Prédiction: Attendre", foreground="blue")

        logger.info(f"✅ Manche traitée: P/B#{pb_number} {result} {parity} {hand_data['sync_state']} {so_conversion} → Prédiction: {prediction}")

    # ------------------------------------------------------------------------
    # 🎮 3.4 CONTRÔLES ET ACTIONS UTILISATEUR
    # ------------------------------------------------------------------------

    def initialiser_burn(self, burn_parity: str):
        """Initialise les cartes brûlées"""
        if self.burn_initialized:
            return

        self.current_game['initialization']['burn_parity'] = burn_parity
        self.current_game['initialization']['initial_sync_state'] = 'SYNC' if burn_parity == 'PAIR' else 'DESYNC'
        self.current_sync_state = self.current_game['initialization']['initial_sync_state']

        self.burn_initialized = True

        # Initialiser le modèle AZR avec la parité du brûlage
        if self.azr_predictor:
            self.azr_predictor.set_burn_parity(burn_parity)

        logger.info(f"🔥 Cartes brûlées initialisées: {burn_parity} → {self.current_sync_state}")

        # Désactiver les boutons de brûlage
        self.burn_pair_btn.config(state=tk.DISABLED)
        self.burn_impair_btn.config(state=tk.DISABLED)

    def reset_partie(self):
        """Remet à zéro la partie en cours comme une nouvelle partie"""
        # Réinitialiser l'interface (sans confirmation pour plus de fluidité)
        self.burn_initialized = False
        self.current_pb_number = self.config.one_value
        self.current_sync_state = 'SYNC'
        self.last_pb_result = None
        self.last_so_conversion = '--'

        # Réinitialiser les données de la partie
        self.current_game = {
            'game_number': self.config.one_value,
            'initialization': {
                'burn_cards_count': self.config.zero_value,
                'burn_parity': 'PAIR',
                'initial_sync_state': 'SYNC'
            },
            'statistics': {
                'total_hands': self.config.zero_value,
                'pb_hands': self.config.zero_value,
                'tie_hands': self.config.zero_value,
                'so_conversions': self.config.zero_value
            },
            'hands': []
        }

        # Réinitialiser le modèle AZR pour une nouvelle session
        if self.azr_predictor:
            self.azr_predictor.reset_session()

        # Remettre le compteur à 1 et réinitialiser la prédiction
        self.counter_label.config(text="Manche P/B: 1")
        self.prediction_label.config(text="Prédiction: Attendre", foreground="blue")

        # Réactiver les boutons de cartes brûlées
        self.burn_pair_btn.config(state=tk.NORMAL)
        self.burn_impair_btn.config(state=tk.NORMAL)

        logger.info("🔄 Nouvelle partie initialisée - Compteur remis à 1")

    def run(self):
        """Lance l'interface graphique"""
        logger.info("🚀 Lancement de l'interface graphique AZR ultra-simplifiée")

        # S'assurer que la fenêtre est visible
        self.root.deiconify()  # Restaurer si minimisée
        self.root.lift()       # Amener au premier plan
        self.root.focus_force() # Forcer le focus

        # Centrer la fenêtre sur l'écran
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

        self.root.mainloop()

# ============================================================================
# 🧠 4. CLASSE PRINCIPALE AZR - CŒUR DU MODÈLE
# ============================================================================
#
# ⚠️  SECTION ULTRA-CRITIQUE - MODÈLE AZR COMPLET
#
# Cette classe contient l'INTÉGRALITÉ du modèle AZR :
# - Architecture Proposeur/Résolveur
# - Algorithmes de prédiction révolutionnaires
# - Apprentissage adaptatif
# - Métriques de performance
#
# MAINTENANCE AZR : Toutes les modifications du modèle se font ICI
# ============================================================================

class AZRBaccaratPredictor:
    """
    Modèle AZR pour prédictions baccarat

    Architecture basée sur les principes AZR :
    - Rôle proposeur : Génère des hypothèses de patterns
    - Rôle résolveur : Valide et prédit la prochaine manche
    - Apprentissage adaptatif : Améliore les performances
    """

    def __init__(self, config: Optional[AZRConfig] = None):
        self.config = config or AZRConfig()

        # Mémoire des séquences
        self.sequence_history: List[str] = []  # Séquence PAIR/IMPAIR complète
        self.hands_history: List[BaccaratHand] = []
        self.predictions_history: List[str] = []
        self.accuracy_history: List[float] = []

        # État de synchronisation
        self.current_sync_state = "SYNC"  # État initial
        self.sync_transitions = []

        # ====================================================================
        # 🧠 MÉTRIQUES AZR CŒUR - ÉTAT INTERNE DU MODÈLE
        # ====================================================================
        # ⚠️  Variables critiques pour le fonctionnement AZR
        # baseline_propose/solve : Métriques de performance des rôles AZR
        # learnability/diversity : Scores d'apprentissage et exploration
        # ====================================================================
        self.baseline_propose = float(self.config.zero_value)
        self.baseline_solve = float(self.config.zero_value)
        self.learnability_scores = deque(maxlen=self.config.learnability_buffer_size)
        self.diversity_scores = deque(maxlen=self.config.diversity_buffer_size)

        # Patterns découverts
        self.discovered_patterns = {}
        self.pattern_success_rates = defaultdict(list)

        # Compteurs de performance
        self.total_predictions = self.config.zero_value
        self.correct_predictions = self.config.zero_value
        self.current_accuracy = float(self.config.zero_value)

        # État du brûlage
        self.burn_parity_set = False

        # Analyse des intervalles SYNC/DESYNC
        self.interval_analysis = {
            'sync_intervals': [],
            'desync_intervals': [],
            'current_interval': None
        }

        # 💾 PERSISTANCE AZR - Compteur pour sauvegarde automatique
        self.predictions_since_save = self.config.zero_value
        self.intelligence_version = "1.0.0"  # Version de l'intelligence

        # Composants intégrés avec configuration
        self.generator = BaccaratGenerator(self.config)
        self.data_loader = BaccaratDataLoader()

        # 🎯 SYSTÈME AZR MASTER - 8 CLUSTERS PARALLÈLES
        self.azr_master = AZRMaster(self.config)

        # 💾 CHARGEMENT AUTOMATIQUE de l'intelligence précédente
        self._load_azr_intelligence()

        logger.info("🧠 Modèle AZR Baccarat initialisé avec persistance intelligente")
        logger.info(f"🎯 Système AZR Master activé: {len(self.azr_master.clusters)} clusters parallèles")

    # ========================================================================
    # 🎯 4.1 INTERFACE PRINCIPALE (receive_hand_data)
    # ========================================================================

    def receive_hand_data(self, hand_data: Dict[str, Any]) -> str:
        """
        Reçoit les données d'une manche de l'interface graphique
        et retourne la prédiction pour la manche suivante

        Args:
            hand_data: Dictionnaire avec pb_hand_number, result, parity, sync_state, so_conversion

        Returns:
            Prédiction 'S' ou 'O' pour la manche suivante
        """
        # Calcul de l'état combiné
        combined_state = f"{hand_data['parity']}_{hand_data['sync_state']}"

        # Conversion en objet BaccaratHand
        hand = BaccaratHand(
            pb_hand_number=hand_data['pb_hand_number'],
            result=hand_data['result'],
            parity=hand_data['parity'],
            sync_state=hand_data['sync_state'],
            so_conversion=hand_data['so_conversion'],
            combined_state=combined_state
        )

        # Mise à jour de l'historique
        self.hands_history.append(hand)
        self.sequence_history.append(hand.parity)
        self.current_sync_state = hand.sync_state

        # Suivi des intervalles SYNC/DESYNC pour l'analyse statistique
        self._track_sync_desync_intervals(hand)

        # Validation de la prédiction précédente si applicable
        if len(self.predictions_history) > self.config.zero_value and hand.so_conversion in ['S', 'O']:
            self._validate_previous_prediction(hand.so_conversion)

        # 🎯 PRÉDICTION AZR MASTER - 8 CLUSTERS PARALLÈLES
        final_prediction = self._predict_with_azr_master()

        # ========================================================================
        # 💤 SYSTÈME CLASSIQUE DÉSACTIVÉ (remplacé par AZR Master)
        # ========================================================================
        # # NOUVELLE PRÉDICTION basée sur l'index combiné
        # combined_prediction = self._predict_with_combined_index()
        #
        # # Génération de la prédiction pour la manche suivante (méthode classique)
        # classic_prediction = self._generate_prediction()
        #
        # # Fusion des prédictions (priorité à l'index combiné si confiance élevée)
        # final_prediction = self._merge_predictions(combined_prediction, classic_prediction)
        # ========================================================================

        self.predictions_history.append(final_prediction)

        # Apprentissage adaptatif
        self._adaptive_learning()

        # 💾 SAUVEGARDE AUTOMATIQUE de l'intelligence AZR
        self._auto_save_intelligence()

        logger.info(f"📊 Manche #{hand.pb_hand_number}: {hand.result} {hand.parity} {hand.sync_state} {hand.combined_state} → Prédiction: {final_prediction}")

        return final_prediction

    def _validate_previous_prediction(self, actual_result: str):
        """Valide la prédiction précédente et met à jour les métriques"""
        if len(self.predictions_history) == 0:
            return

        last_prediction = self.predictions_history[-1]
        is_correct = (last_prediction == actual_result)

        if is_correct:
            self.correct_predictions += self.config.one_value

        self.total_predictions += self.config.one_value
        self.current_accuracy = self.correct_predictions / self.total_predictions
        self.accuracy_history.append(self.current_accuracy)

        logger.info(f"✅ Validation: Prédit={last_prediction}, Réel={actual_result}, Correct={is_correct}, Précision={self.current_accuracy:.3f}")

    # ========================================================================
    # 💾 PERSISTANCE AZR - MÉMOIRE INTELLIGENTE CONTINUE
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - PERSISTANCE DE L'INTELLIGENCE AZR
    #
    # Système de sauvegarde/chargement automatique qui préserve l'intelligence
    # acquise entre les sessions. L'AZR ne "redémarre" jamais - il ÉVOLUE !
    #
    # MAINTENANCE : Modifications = Impact sur continuité intelligence
    # ========================================================================

    def _load_azr_intelligence(self):
        """
        💾 CHARGEMENT AUTOMATIQUE de l'intelligence AZR précédente

        Charge l'état sauvegardé pour continuer l'amélioration depuis le niveau atteint
        """
        try:
            # Chargement de l'état principal
            if os.path.exists(self.config.azr_state_file):
                with open(self.config.azr_state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                # Restauration des métriques de performance
                self.total_predictions = state_data.get('total_predictions', self.config.zero_value)
                self.correct_predictions = state_data.get('correct_predictions', self.config.zero_value)
                self.current_accuracy = state_data.get('current_accuracy', float(self.config.zero_value))
                self.intelligence_version = state_data.get('intelligence_version', "1.0.0")

                # Restauration des historiques (limités pour performance)
                max_history = 100  # Garder seulement les 100 dernières
                self.accuracy_history = state_data.get('accuracy_history', [])[-max_history:]
                self.predictions_history = state_data.get('predictions_history', [])[-max_history:]

                logger.info(f"💾 Intelligence AZR chargée: v{self.intelligence_version}, "
                          f"Précision: {self.current_accuracy:.3f}, "
                          f"Prédictions: {self.total_predictions}")

            # Chargement des baselines adaptatifs
            if os.path.exists(self.config.azr_baselines_file):
                with open(self.config.azr_baselines_file, 'r', encoding='utf-8') as f:
                    baselines_data = json.load(f)

                self.baseline_propose = baselines_data.get('baseline_propose', self.config.zero_value)
                self.baseline_solve = baselines_data.get('baseline_solve', self.config.zero_value)

                logger.info(f"💾 Baselines AZR chargés: propose={self.baseline_propose:.3f}, "
                          f"solve={self.baseline_solve:.3f}")

            # Chargement des découvertes validées
            if os.path.exists(self.config.azr_discoveries_file):
                with open(self.config.azr_discoveries_file, 'r', encoding='utf-8') as f:
                    discoveries_data = json.load(f)

                self.discovered_patterns = discoveries_data.get('patterns', {})
                self.pattern_success_rates = defaultdict(list, discoveries_data.get('success_rates', {}))

                logger.info(f"💾 Découvertes AZR chargées: {len(self.discovered_patterns)} patterns")

            logger.info("🧠 Intelligence AZR restaurée avec succès - Continuité assurée !")

        except Exception as e:
            logger.warning(f"⚠️ Erreur chargement intelligence AZR: {e}")
            logger.info("🆕 Démarrage avec intelligence AZR vierge")

    def _auto_save_intelligence(self):
        """
        💾 SAUVEGARDE AUTOMATIQUE de l'intelligence AZR

        Sauvegarde l'état actuel selon la fréquence configurée
        """
        if not self.config.auto_save_enabled:
            return

        self.predictions_since_save += self.config.one_value

        # Sauvegarder selon la fréquence configurée
        if self.predictions_since_save >= self.config.save_frequency:
            self._save_azr_intelligence()
            self.predictions_since_save = self.config.zero_value

    def _save_azr_intelligence(self):
        """
        💾 SAUVEGARDE COMPLÈTE de l'intelligence AZR

        Sauvegarde tous les composants de l'intelligence acquise
        """
        try:
            # Créer le dossier de sauvegarde si nécessaire
            os.makedirs(self.config.azr_backup_dir, exist_ok=True)

            # 1. SAUVEGARDE ÉTAT PRINCIPAL
            state_data = {
                'intelligence_version': self.intelligence_version,
                'timestamp': datetime.now().isoformat(),
                'total_predictions': self.total_predictions,
                'correct_predictions': self.correct_predictions,
                'current_accuracy': self.current_accuracy,
                'accuracy_history': self.accuracy_history[-100:],  # 100 dernières
                'predictions_history': self.predictions_history[-100:],  # 100 dernières
                'session_info': {
                    'hands_played': len(self.hands_history),
                    'current_sync_state': self.current_sync_state
                }
            }

            with open(self.config.azr_state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)

            # 2. SAUVEGARDE BASELINES ADAPTATIFS
            baselines_data = {
                'timestamp': datetime.now().isoformat(),
                'baseline_propose': float(self.baseline_propose),
                'baseline_solve': float(self.baseline_solve),
                'adaptation_info': {
                    'learning_rate': self.config.learning_rate,
                    'baseline_alpha': self.config.baseline_alpha
                }
            }

            with open(self.config.azr_baselines_file, 'w', encoding='utf-8') as f:
                json.dump(baselines_data, f, indent=2, ensure_ascii=False)

            # 3. SAUVEGARDE DÉCOUVERTES VALIDÉES
            discoveries_data = {
                'timestamp': datetime.now().isoformat(),
                'intelligence_version': self.intelligence_version,
                'patterns': dict(self.discovered_patterns),
                'success_rates': dict(self.pattern_success_rates),
                'validation_info': {
                    'total_patterns': len(self.discovered_patterns),
                    'avg_success_rate': self.current_accuracy
                }
            }

            with open(self.config.azr_discoveries_file, 'w', encoding='utf-8') as f:
                json.dump(discoveries_data, f, indent=2, ensure_ascii=False)

            # 4. SAUVEGARDE DE BACKUP HORODATÉE
            if self.config.version_discoveries:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = os.path.join(self.config.azr_backup_dir, f"azr_backup_{timestamp}.json")

                backup_data = {
                    'state': state_data,
                    'baselines': baselines_data,
                    'discoveries': discoveries_data
                }

                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, ensure_ascii=False)

                # Nettoyage des anciens backups
                self._cleanup_old_backups()

            logger.info(f"💾 Intelligence AZR sauvegardée: v{self.intelligence_version}, "
                      f"Précision: {self.current_accuracy:.3f}")

        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde intelligence AZR: {e}")

    def _cleanup_old_backups(self):
        """Nettoie les anciens backups selon la configuration"""
        try:
            if not os.path.exists(self.config.azr_backup_dir):
                return

            # Lister tous les fichiers de backup
            backup_files = []
            for filename in os.listdir(self.config.azr_backup_dir):
                if filename.startswith('azr_backup_') and filename.endswith('.json'):
                    filepath = os.path.join(self.config.azr_backup_dir, filename)
                    backup_files.append((filepath, os.path.getmtime(filepath)))

            # Trier par date (plus récent en premier)
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # Supprimer les backups excédentaires
            if len(backup_files) > self.config.max_backups:
                for filepath, _ in backup_files[self.config.max_backups:]:
                    os.remove(filepath)
                    logger.debug(f"🗑️ Backup supprimé: {os.path.basename(filepath)}")

        except Exception as e:
            logger.warning(f"⚠️ Erreur nettoyage backups: {e}")

    # ========================================================================
    # 🎯 4.2 PRÉDICTION PAR INDEX COMBINÉ AZR (RÉVOLUTIONNAIRE)
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - INTELLIGENCE AZR RÉVOLUTIONNAIRE
    #
    # Cette méthode utilise les règles découvertes par analyse de 1M+ parties
    # Index combiné PAIR/IMPAIR + SYNC/DESYNC = Prédictions optimales
    #
    # MAINTENANCE : NE PAS modifier sans validation statistique complète
    # ========================================================================

    def _predict_with_combined_index(self) -> Dict[str, Any]:
        """
        🔬 MODE EXPLORATION AVEUGLE - DÉCOUVERTES MASQUÉES

        Cette méthode est temporairement désactivée pour permettre aux rollouts
        d'explorer sans connaître les patterns découverts et tester leur capacité
        à redécouvrir indépendamment les règles révolutionnaires.

        Returns:
            Dict avec prediction neutre (exploration aveugle)
        """
        # 🔬 EXPLORATION AVEUGLE : Retourner prédiction neutre
        if not self.config.use_combined_index_discovery:
            return {
                'prediction': 'S' if random.random() > 0.5 else 'O',  # Aléatoire
                'confidence': 0.1,  # Confiance très faible
                'method': 'blind_exploration'
            }

        # Code original (masqué pendant exploration aveugle)
        if len(self.hands_history) == 0:
            return {
                'prediction': 'S',
                'confidence': 0.1,
                'method': 'default_combined'
            }

        # Obtenir l'état combiné actuel
        current_hand = self.hands_history[-1]
        current_combined_state = current_hand.combined_state

        # Appliquer les règles découvertes
        if current_combined_state in self.config.combined_prediction_rules:
            rule = self.config.combined_prediction_rules[current_combined_state]

            prediction = rule['preferred_prediction']
            base_confidence = rule['confidence_bonus']

            # Ajuster la confiance selon le taux de réussite
            if prediction == 'S':
                success_rate = rule['S_rate']
            else:
                success_rate = rule['O_rate']

            # Confiance finale = taux de réussite + bonus (limitée par la configuration)
            final_confidence = min(self.config.probability_clamp_max * 0.9, success_rate + base_confidence)

            logger.info(f"🎯 Index Combiné: {current_combined_state} → {prediction} (confiance: {final_confidence:.1%})")

            return {
                'prediction': prediction,
                'confidence': final_confidence,
                'method': 'combined_index',
                'state': current_combined_state,
                'success_rate': success_rate
            }

        # État combiné non reconnu, prédiction par défaut
        return {
            'prediction': 'S',
            'confidence': 0.1,
            'method': 'unknown_combined_state'
        }

    def _merge_predictions(self, combined_pred: Dict[str, Any], classic_pred: str) -> str:
        """
        Fusionne les prédictions de l'index combiné et de la méthode classique

        Args:
            combined_pred: Prédiction basée sur l'index combiné
            classic_pred: Prédiction de la méthode classique AZR

        Returns:
            Prédiction finale
        """
        combined_confidence = combined_pred.get('confidence', 0.0)

        # Seuil de confiance configuré pour privilégier l'index combiné
        confidence_threshold = self.config.combined_confidence_threshold

        if combined_confidence >= confidence_threshold:
            # Confiance élevée dans l'index combiné, l'utiliser
            logger.info(f"🎯 Prédiction finale: Index Combiné ({combined_pred['prediction']}) - Confiance: {combined_confidence:.1%}")
            return combined_pred['prediction']

        elif combined_confidence >= self.config.combined_moderate_threshold:
            # Confiance modérée, vérifier la cohérence
            if combined_pred['prediction'] == classic_pred:
                # Les deux méthodes s'accordent, renforcer la confiance
                logger.info(f"🤝 Prédiction finale: Accord des méthodes ({classic_pred}) - Confiance renforcée")
                return classic_pred
            else:
                # Désaccord, privilégier l'index combiné si > 50%
                if combined_confidence > self.config.probability_neutral:
                    logger.info(f"⚖️ Prédiction finale: Index Combiné prioritaire ({combined_pred['prediction']})")
                    return combined_pred['prediction']
                else:
                    logger.info(f"⚖️ Prédiction finale: Méthode classique prioritaire ({classic_pred})")
                    return classic_pred

        else:
            # Confiance faible dans l'index combiné, utiliser la méthode classique
            logger.info(f"📊 Prédiction finale: Méthode classique ({classic_pred}) - Index combiné peu fiable")
            return classic_pred

    # ========================================================================
    # 🎯 4.2 PRÉDICTION AZR MASTER - 8 CLUSTERS PARALLÈLES
    # ========================================================================

    def _predict_with_azr_master(self) -> str:
        """
        Prédiction utilisant le système AZR Master avec 8 clusters parallèles

        Chaque cluster exécute ses 3 rollouts (Analyseur → Générateur → Prédicteur)
        puis un consensus intelligent est construit à partir des 8 résultats.

        Returns:
            'S' ou 'O' basé sur le consensus des 8 clusters
        """
        try:
            # Préparation de la séquence standardisée pour les clusters
            standardized_sequence = self._prepare_sequence_for_clusters()

            # Exécution du système AZR Master (8 clusters en parallèle)
            master_result = self.azr_master.predict_next_sequence(standardized_sequence)

            # Extraction de la prédiction consensus
            consensus = master_result.get('consensus_prediction', {})
            final_prediction = consensus.get('consensus_so', 'S')
            consensus_confidence = consensus.get('consensus_confidence', 0.5)
            agreement_level = consensus.get('agreement_level', 0.0)

            # Logging détaillé du processus
            timing_metrics = master_result.get('timing_metrics', {})
            clusters_completed = timing_metrics.get('clusters_completed', 0)
            total_time = timing_metrics.get('total_system_time_ms', 0)

            logger.info(f"🎯 AZR Master: {clusters_completed}/8 clusters → Consensus: {final_prediction} "
                       f"(confiance: {consensus_confidence:.1%}, accord: {agreement_level:.1%}, "
                       f"temps: {total_time:.1f}ms)")

            # Bonus si unanimité des 8 clusters
            voting_details = consensus.get('voting_details', {})
            if voting_details.get('unanimous_agreement', False):
                logger.info(f"🚀 UNANIMITÉ des 8 clusters AZR → Confiance boostée !")

            return final_prediction

        except Exception as e:
            logger.error(f"❌ Erreur AZR Master: {e}")
            # Fallback sur prédiction par défaut
            return self._default_prediction()

    def _prepare_sequence_for_clusters(self) -> Dict:
        """
        Prépare la séquence complète depuis le brûlage pour les clusters AZR

        Returns:
            Dict: Séquence standardisée avec toutes les données nécessaires
        """
        # Conversion de l'historique en format standardisé pour les clusters
        hands_history = []

        for hand in self.hands_history:
            hands_history.append(hand)  # Les objets BaccaratHand sont déjà au bon format

        # Données de brûlage (si disponibles)
        burn_data = self._get_burn_data_for_rollout() if hasattr(self, '_get_burn_data_for_rollout') else {
            'burn_parity': 'IMPAIR',  # Valeur par défaut
            'initial_sync_state': 'DESYNC'
        }

        standardized_sequence = {
            'hands_history': hands_history,
            'burn_data': burn_data,
            'sequence_metadata': {
                'total_hands': len(hands_history),
                'current_sync_state': self.current_sync_state,
                'sequence_length': len(self.sequence_history)
            }
        }

        return standardized_sequence

    # ========================================================================
    # 🔮 4.3 GÉNÉRATION DE PRÉDICTIONS AZR (MÉTHODE CLASSIQUE - DÉSACTIVÉE)
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - ALGORITHME AZR TRADITIONNEL
    #
    # Méthode classique AZR utilisant Proposeur → Résolveur → Prédiction
    # Sert de fallback et de validation pour l'index combiné
    #
    # MAINTENANCE : Modifications = Impact sur logique AZR de base
    # ========================================================================

    def _generate_prediction(self) -> str:
        """
        Génère une prédiction S/O basée sur l'analyse AZR de la séquence complète

        Returns:
            'S' pour Same ou 'O' pour Opposite
        """
        if len(self.sequence_history) < 2:
            return self._default_prediction()

        # Phase 1: Rôle Proposeur - Génération d'hypothèses
        hypotheses = self._propose_hypotheses()

        # Phase 2: Rôle Résolveur - Validation et sélection
        best_hypothesis = self._solve_best_hypothesis(hypotheses)

        # Phase 3: Génération de la prédiction finale
        prediction = self._finalize_prediction(best_hypothesis)

        return prediction

    def _default_prediction(self) -> str:
        """Prédiction par défaut quand pas assez de données"""
        # Basé sur l'état de synchronisation actuel
        if self.current_sync_state == "SYNC":
            return "S"  # Tendance à continuer en mode synchrone
        else:
            return "O"  # Tendance à alterner en mode désynchrone

    # ========================================================================
    # 🎭 5. RÔLE PROPOSEUR AZR - CŒUR ALGORITHMIQUE
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - ALGORITHME PROPOSEUR AZR
    #
    # Le Proposeur génère des hypothèses de patterns avec métriques de learnability
    # Implémentation directe des principes AZR d'Absolute Zero Reasoning
    #
    # MAINTENANCE : Modifications = Impact sur génération d'hypothèses
    # ========================================================================

    def _propose_hypotheses(self) -> List[Dict[str, Any]]:
        """
        Rôle Proposeur AZR : Génère des hypothèses de patterns

        Returns:
            Liste d'hypothèses avec leurs métriques de learnability
        """
        hypotheses = []

        # Hypothèse 1: Pattern de synchronisation
        sync_hypothesis = self._analyze_sync_patterns()
        if sync_hypothesis:
            hypotheses.append({
                'type': 'sync_pattern',
                'prediction': sync_hypothesis['prediction'],
                'confidence': sync_hypothesis['confidence'],
                'learnability': self._calculate_learnability(sync_hypothesis)
            })

        # Hypothèse 2: Pattern de parité
        parity_hypothesis = self._analyze_parity_patterns()
        if parity_hypothesis:
            hypotheses.append({
                'type': 'parity_pattern',
                'prediction': parity_hypothesis['prediction'],
                'confidence': parity_hypothesis['confidence'],
                'learnability': self._calculate_learnability(parity_hypothesis)
            })

        # Hypothèse 3: Pattern de séquence récente
        recent_hypothesis = self._analyze_recent_patterns()
        if recent_hypothesis:
            hypotheses.append({
                'type': 'recent_pattern',
                'prediction': recent_hypothesis['prediction'],
                'confidence': recent_hypothesis['confidence'],
                'learnability': self._calculate_learnability(recent_hypothesis)
            })

        # Hypothèse 4: Pattern de rollouts (exploration)
        rollout_hypotheses = self._generate_rollout_hypotheses()
        hypotheses.extend(rollout_hypotheses)

        return hypotheses

    def _analyze_sync_patterns(self) -> Optional[Dict[str, Any]]:
        """Analyse les patterns de synchronisation SYNC/DESYNC"""
        if len(self.hands_history) < 3:
            return None

        # Analyse des transitions de synchronisation
        sync_transitions = []
        for i in range(1, len(self.hands_history)):
            prev_sync = self.hands_history[i-1].sync_state
            curr_sync = self.hands_history[i].sync_state
            so_result = self.hands_history[i].so_conversion

            if so_result in ['S', 'O']:
                sync_transitions.append({
                    'from': prev_sync,
                    'to': curr_sync,
                    'result': so_result
                })

        if not sync_transitions:
            return None

        # Calcul des probabilités conditionnelles
        current_sync = self.current_sync_state
        same_prob = 0.0
        total_count = 0

        for transition in sync_transitions:
            if transition['from'] == current_sync:
                total_count += 1
                if transition['result'] == 'S':
                    same_prob += 1

        if total_count == 0:
            return None

        same_prob /= total_count
        confidence = abs(same_prob - 0.5) * 2  # Distance de 50%

        prediction = 'S' if same_prob > 0.5 else 'O'

        return {
            'prediction': prediction,
            'confidence': confidence,
            'probability': same_prob,
            'sample_size': total_count
        }

    def _analyze_parity_patterns(self) -> Optional[Dict[str, Any]]:
        """Analyse les patterns dans la séquence PAIR/IMPAIR"""
        if len(self.sequence_history) < self.config.pattern_min_length:
            return None

        # Recherche de patterns répétitifs
        sequence = self.sequence_history
        pattern_scores = {}

        # Test de patterns de longueur 2 à 8
        for pattern_length in range(2, min(9, len(sequence) // 2)):
            pattern = sequence[-pattern_length:]
            pattern_key = ''.join([p[0] for p in pattern])  # P ou I

            # Recherche d'occurrences du pattern
            occurrences = []
            for i in range(len(sequence) - pattern_length):
                test_pattern = sequence[i:i+pattern_length]
                test_key = ''.join([p[0] for p in test_pattern])

                if test_key == pattern_key:
                    # Vérifier ce qui suit le pattern
                    if i + pattern_length < len(self.hands_history):
                        next_hand = self.hands_history[i + pattern_length]
                        if next_hand.so_conversion in ['S', 'O']:
                            occurrences.append(next_hand.so_conversion)

            if len(occurrences) >= 2:  # Au moins 2 occurrences
                same_count = occurrences.count('S')
                total_count = len(occurrences)
                same_prob = same_count / total_count

                confidence = abs(same_prob - 0.5) * 2
                pattern_scores[pattern_key] = {
                    'prediction': 'S' if same_prob > 0.5 else 'O',
                    'confidence': confidence,
                    'probability': same_prob,
                    'occurrences': total_count
                }

        if not pattern_scores:
            return None

        # Sélection du meilleur pattern
        best_pattern = max(pattern_scores.items(),
                          key=lambda x: x[1]['confidence'] * math.log(x[1]['occurrences'] + 1))

        return best_pattern[1]

    def _analyze_recent_patterns(self) -> Optional[Dict[str, Any]]:
        """Analyse les patterns dans les manches récentes (fenêtre glissante)"""
        window_size = min(10, len(self.hands_history))
        if window_size < 3:
            return None

        recent_hands = self.hands_history[-window_size:]
        recent_so = [h.so_conversion for h in recent_hands if h.so_conversion in ['S', 'O']]

        if len(recent_so) < 2:
            return None

        # Analyse de la tendance récente
        same_count = recent_so.count('S')
        total_count = len(recent_so)
        same_prob = same_count / total_count

        # Pondération par récence (plus récent = plus important)
        weighted_same = 0.0
        total_weight = 0.0

        for i, result in enumerate(recent_so):
            weight = (i + 1) / len(recent_so)  # Poids croissant
            total_weight += weight
            if result == 'S':
                weighted_same += weight

        weighted_prob = weighted_same / total_weight
        confidence = abs(weighted_prob - 0.5) * 2

        return {
            'prediction': 'S' if weighted_prob > 0.5 else 'O',
            'confidence': confidence,
            'probability': weighted_prob,
            'window_size': window_size
        }

    def _generate_rollout_hypotheses(self) -> List[Dict[str, Any]]:
        """
        Génère des hypothèses par rollouts (exploration AZR)
        OPTIMISÉ CPU 8 CŒURS : Parallélisation automatique des rollouts

        Returns:
            Liste d'hypothèses générées par exploration parallélisée
        """
        if not self.config.parallel_rollouts or self.config.n_rollouts <= 4:
            # Mode séquentiel pour petits nombres de rollouts
            return self._generate_rollout_hypotheses_sequential()

        # Mode parallélisé optimisé CPU 8 cœurs
        return self._generate_rollout_hypotheses_parallel()

    def _generate_rollout_hypotheses_sequential(self) -> List[Dict[str, Any]]:
        """
        🚀 CORRECTION CRITIQUE - Version séquentielle des rollouts OPTIMISÉE
        CONSTRUCTION UNIQUE de la séquence partagée entre TOUS les rollouts

        AVANT : 16 rollouts × reconstruction complète = 16x duplication !
        APRÈS : 1 construction + 16 rollouts = Performance optimale !
        """
        # 🚀 CONSTRUCTION UNIQUE - Plus jamais de duplication !
        shared_complete_sequence = self._build_complete_sequence_for_rollout()

        rollout_hypotheses = []

        for i in range(self.config.n_rollouts):
            # Paramètres de rollout avec variation
            temperature = self.config.rollout_temperature + (i * self.config.rollout_step_size)
            random_factor = self.config.rollout_random_factor * (1 + i * 0.1)

            # 🚀 ROLLOUT OPTIMISÉ - Utilise la séquence partagée
            hypothesis = self._single_rollout_with_shared_sequence(
                shared_complete_sequence, temperature, random_factor)
            if hypothesis:
                hypothesis['type'] = f'rollout_{i}'
                rollout_hypotheses.append(hypothesis)

        return rollout_hypotheses

    def _generate_rollout_hypotheses_parallel(self) -> List[Dict[str, Any]]:
        """
        🚀 CORRECTION CRITIQUE - Version parallélisée des rollouts OPTIMISÉE
        CONSTRUCTION UNIQUE de la séquence partagée entre TOUS les rollouts parallèles

        AVANT : 16 threads × reconstruction complète = 16x duplication !
        APRÈS : 1 construction + 16 threads = Performance optimale !
        """
        # 🚀 CONSTRUCTION UNIQUE - Plus jamais de duplication !
        shared_complete_sequence = self._build_complete_sequence_for_rollout()

        rollout_hypotheses = []

        # Préparer les paramètres pour tous les rollouts
        rollout_params = []
        for i in range(self.config.n_rollouts):
            temperature = self.config.rollout_temperature + (i * self.config.rollout_step_size)
            random_factor = self.config.rollout_random_factor * (1 + i * 0.1)
            rollout_params.append((i, temperature, random_factor, shared_complete_sequence))

        # Exécution parallèle sur 8 cœurs
        with ThreadPoolExecutor(max_workers=self.config.thread_pool_size) as executor:
            # 🚀 ROLLOUTS OPTIMISÉS - Utilisent la séquence partagée
            future_to_params = {
                executor.submit(self._single_rollout_with_shared_sequence_parallel, params): params
                for params in rollout_params
            }

            # Collecter les résultats
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                try:
                    hypothesis = future.result()
                    if hypothesis:
                        hypothesis['type'] = f'rollout_{params[0]}'
                        rollout_hypotheses.append(hypothesis)
                except Exception as exc:
                    logger.warning(f'Rollout {params[0]} généré une exception: {exc}')

        return rollout_hypotheses

    def _single_rollout_with_shared_sequence(self, shared_sequence: Dict[str, Any],
                                           temperature: float, random_factor: float) -> Optional[Dict[str, Any]]:
        """
        🚀 ROLLOUT OPTIMISÉ - Utilise une séquence partagée (mode séquentiel)

        Args:
            shared_sequence: Séquence complète pré-construite (partagée)
            temperature: Température du rollout
            random_factor: Facteur aléatoire du rollout
        """
        if len(self.sequence_history) < self.config.two_value:
            return None

        # 🚀 PLUS DE RECONSTRUCTION ! Utilise directement la séquence partagée
        base_prediction = self._analyze_complete_sequence_for_rollout(shared_sequence, temperature, random_factor)

        # Application du bruit contrôlé selon la configuration
        noise = np.random.normal(self.config.zero_value, random_factor)
        adjusted_prob = (self.config.probability_neutral +
                        (base_prediction - self.config.probability_neutral) * temperature +
                        noise)
        adjusted_prob = max(self.config.probability_clamp_min,
                          min(self.config.probability_clamp_max, adjusted_prob))

        prediction = 'S' if adjusted_prob > self.config.probability_neutral else 'O'
        confidence = abs(adjusted_prob - self.config.probability_neutral) * self.config.two_value

        return {
            'prediction': prediction,
            'confidence': confidence,
            'probability': adjusted_prob,
            'temperature': temperature,
            'noise': noise,
            'sequence_analysis': shared_sequence,  # Référence à la séquence partagée
            'rollout_type': 'optimized_shared_sequence'
        }

    def _single_rollout_with_shared_sequence_parallel(self, params: Tuple[int, float, float, Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        🚀 ROLLOUT OPTIMISÉ - Utilise une séquence partagée (mode parallèle)

        Args:
            params: Tuple (index, temperature, random_factor, shared_sequence)
        """
        index, temperature, random_factor, shared_sequence = params

        if len(self.sequence_history) < self.config.two_value:
            return None

        # 🚀 PLUS DE RECONSTRUCTION ! Utilise directement la séquence partagée
        base_prediction = self._analyze_complete_sequence_for_rollout(shared_sequence, temperature, random_factor)

        # Application du bruit contrôlé selon la configuration
        noise = np.random.normal(self.config.zero_value, random_factor)
        adjusted_prob = (self.config.probability_neutral +
                        (base_prediction - self.config.probability_neutral) * temperature +
                        noise)
        adjusted_prob = max(self.config.probability_clamp_min,
                          min(self.config.probability_clamp_max, adjusted_prob))

        prediction = 'S' if adjusted_prob > self.config.probability_neutral else 'O'
        confidence = abs(adjusted_prob - self.config.probability_neutral) * self.config.two_value

        return {
            'prediction': prediction,
            'confidence': confidence,
            'probability': adjusted_prob,
            'temperature': temperature,
            'noise': noise,
            'sequence_analysis': shared_sequence,  # Référence à la séquence partagée
            'rollout_type': 'optimized_shared_sequence_parallel',
            'rollout_index': index
        }

    def _single_rollout_with_params(self, params: Tuple[int, float, float]) -> Optional[Dict[str, Any]]:
        """
        🔧 MÉTHODE LEGACY - Wrapper pour rollout avec paramètres (rétrocompatibilité)
        ⚠️  ATTENTION : Cette méthode utilise encore l'ancienne approche avec duplication !

        Args:
            params: Tuple (index, temperature, random_factor)
        """
        index, temperature, random_factor = params
        return self._single_rollout(temperature, random_factor)

    def _single_rollout(self, temperature: float, random_factor: float) -> Optional[Dict[str, Any]]:
        """
        🔧 MÉTHODE LEGACY - Génère une seule hypothèse par rollout (AVEC DUPLICATION !)
        ⚠️  ATTENTION : Cette méthode reconstruit la séquence à chaque appel !

        PROBLÈME CRITIQUE : DUPLICATION MASSIVE
        - Chaque rollout reconstruit TOUTE la séquence depuis le brûlage
        - 16 rollouts = 16x duplication des mêmes données !
        - Performance dégradée de 1500% !

        ✅ UTILISER À LA PLACE : _single_rollout_with_shared_sequence()
        """
        if len(self.sequence_history) < self.config.two_value:
            return None

        # ⚠️  DUPLICATION CRITIQUE ! Construire la séquence complète pour le rollout
        complete_sequence = self._build_complete_sequence_for_rollout()

        # Analyse sophistiquée avec accès à toute la séquence
        base_prediction = self._analyze_complete_sequence_for_rollout(complete_sequence, temperature, random_factor)

        # Application du bruit contrôlé selon la configuration
        noise = np.random.normal(self.config.zero_value, random_factor)
        adjusted_prob = (self.config.probability_neutral +
                        (base_prediction - self.config.probability_neutral) * temperature +
                        noise)
        adjusted_prob = max(self.config.probability_clamp_min,
                          min(self.config.probability_clamp_max, adjusted_prob))

        prediction = 'S' if adjusted_prob > self.config.probability_neutral else 'O'
        confidence = abs(adjusted_prob - self.config.probability_neutral) * self.config.two_value

        return {
            'prediction': prediction,
            'confidence': confidence,
            'probability': adjusted_prob,
            'temperature': temperature,
            'noise': noise,
            'sequence_analysis': complete_sequence,  # Inclure l'analyse complète
            'rollout_type': 'complete_sequence_analysis'
        }

    def _get_base_tendency(self) -> float:
        """Calcule la tendance de base de la séquence"""
        if len(self.hands_history) < 2:
            return 0.5

        # Analyse simple des dernières conversions S/O
        recent_so = [h.so_conversion for h in self.hands_history[-5:] if h.so_conversion in ['S', 'O']]

        if not recent_so:
            return 0.5

        same_count = recent_so.count('S')
        return same_count / len(recent_so)

    def _build_complete_sequence_for_rollout(self) -> Dict[str, Any]:
        """
        Construit la séquence complète depuis le brûlage pour les rollouts

        ACCÈS COMPLET :
        - Brûlage initial (PAIR/IMPAIR, SYNC/DESYNC)
        - Toutes les manches avec PAIR/IMPAIR, SYNC/DESYNC, états combinés
        - Tous les résultats P/B/T
        - Toutes les conversions S/O
        """
        # Initialisation avec données de brûlage
        burn_data = self._get_burn_data_for_rollout()

        # Séquences complètes
        pair_impair_sequence = []
        sync_desync_sequence = []
        combined_states_sequence = []
        pbt_results_sequence = []
        so_conversions_sequence = []

        # Index pour tracking
        global_hand_index = self.config.zero_value
        pb_hand_index = self.config.zero_value

        # Ajouter les données de brûlage comme manche 0
        pair_impair_sequence.append(burn_data['burn_parity'])
        sync_desync_sequence.append(burn_data['initial_sync_state'])
        combined_states_sequence.append(f"{burn_data['burn_parity']}_{burn_data['initial_sync_state']}")
        pbt_results_sequence.append('BURN')
        so_conversions_sequence.append('--')

        # Construire les séquences pour toutes les manches
        for hand in self.hands_history:
            global_hand_index += self.config.one_value

            # PAIR/IMPAIR pour cette manche
            pair_impair_sequence.append(hand.parity)

            # SYNC/DESYNC pour cette manche
            sync_desync_sequence.append(hand.sync_state)

            # État combiné PAIR_SYNC/DESYNC ou IMPAIR_SYNC/DESYNC
            combined_state = f"{hand.parity}_{hand.sync_state}"
            combined_states_sequence.append(combined_state)

            # Résultat P/B/T
            result_short = hand.result[self.config.zero_value] if hand.result != 'TIE' else 'T'
            pbt_results_sequence.append(result_short)

            # Conversion S/O (seulement pour P/B)
            if hand.result in ['PLAYER', 'BANKER']:
                pb_hand_index += self.config.one_value
                so_conversions_sequence.append(hand.so_conversion if hand.so_conversion in ['S', 'O'] else '--')
            else:
                so_conversions_sequence.append('--')  # TIE n'a pas de conversion S/O

        # Construire la structure complète
        complete_sequence = {
            'burn_data': burn_data,
            'sequences': {
                'pair_impair': pair_impair_sequence,
                'sync_desync': sync_desync_sequence,
                'combined_states': combined_states_sequence,
                'pbt_results': pbt_results_sequence,
                'so_conversions': so_conversions_sequence
            },
            'indexes': {
                'total_hands': global_hand_index,
                'pb_hands': pb_hand_index,
                'tie_hands': global_hand_index - pb_hand_index,
                'so_conversions_count': len([so for so in so_conversions_sequence if so in ['S', 'O']])
            },
            'statistics': self._calculate_sequence_statistics(pair_impair_sequence, sync_desync_sequence,
                                                           combined_states_sequence, so_conversions_sequence)
        }

        return complete_sequence

    def _get_burn_data_for_rollout(self) -> Dict[str, Any]:
        """Récupère les données de brûlage pour les rollouts"""
        # Si on a des données de brûlage stockées, les utiliser
        if hasattr(self, 'burn_cards') and self.burn_cards:
            burn_parity = 'PAIR' if len(self.burn_cards) % self.config.two_value == self.config.zero_value else 'IMPAIR'
            initial_sync = 'SYNC' if burn_parity == 'PAIR' else 'DESYNC'
            burn_count = len(self.burn_cards)
        else:
            # Données par défaut ou estimation
            burn_parity = 'PAIR'  # Valeur par défaut
            initial_sync = 'SYNC'
            burn_count = self.config.two_value  # Minimum de brûlage

        return {
            'burn_cards_count': burn_count,
            'burn_parity': burn_parity,
            'initial_sync_state': initial_sync
        }

    def _calculate_sequence_statistics(self, pair_impair: List[str], sync_desync: List[str],
                                     combined_states: List[str], so_conversions: List[str]) -> Dict[str, Any]:
        """Calcule les statistiques de la séquence pour les rollouts"""

        # Statistiques PAIR/IMPAIR
        pair_count = pair_impair.count('PAIR')
        impair_count = pair_impair.count('IMPAIR')

        # Statistiques SYNC/DESYNC
        sync_count = sync_desync.count('SYNC')
        desync_count = sync_desync.count('DESYNC')

        # Statistiques états combinés
        pair_sync_count = combined_states.count('PAIR_SYNC')
        pair_desync_count = combined_states.count('PAIR_DESYNC')
        impair_sync_count = combined_states.count('IMPAIR_SYNC')
        impair_desync_count = combined_states.count('IMPAIR_DESYNC')

        # Statistiques S/O
        s_count = so_conversions.count('S')
        o_count = so_conversions.count('O')
        so_total = s_count + o_count

        return {
            'pair_impair_stats': {
                'pair_count': pair_count,
                'impair_count': impair_count,
                'pair_ratio': pair_count / len(pair_impair) if pair_impair else self.config.zero_value
            },
            'sync_desync_stats': {
                'sync_count': sync_count,
                'desync_count': desync_count,
                'sync_ratio': sync_count / len(sync_desync) if sync_desync else self.config.zero_value
            },
            'combined_states_stats': {
                'pair_sync_count': pair_sync_count,
                'pair_desync_count': pair_desync_count,
                'impair_sync_count': impair_sync_count,
                'impair_desync_count': impair_desync_count,
                'total_states': len(combined_states)
            },
            'so_stats': {
                's_count': s_count,
                'o_count': o_count,
                'so_total': so_total,
                's_ratio': s_count / so_total if so_total > self.config.zero_value else self.config.zero_value
            }
        }

    def _analyze_complete_sequence_for_rollout(self, complete_sequence: Dict[str, Any],
                                             temperature: float, random_factor: float) -> float:
        """
        Analyse sophistiquée de la séquence complète pour les rollouts

        ANALYSE MULTI-NIVEAUX :
        1. Patterns PAIR/IMPAIR depuis brûlage
        2. Évolution SYNC/DESYNC
        3. Corrélations états combinés → S/O
        4. Tendances récentes vs historiques
        5. Influence du brûlage sur la séquence
        """
        sequences = complete_sequence['sequences']
        statistics = complete_sequence['statistics']

        # 1. ANALYSE PATTERNS PAIR/IMPAIR COMPLETS
        pair_impair_influence = self._analyze_pair_impair_patterns(sequences['pair_impair'])

        # 2. ANALYSE ÉVOLUTION SYNC/DESYNC
        sync_desync_influence = self._analyze_sync_desync_evolution(sequences['sync_desync'])

        # 3. ANALYSE ÉTATS COMBINÉS → S/O (DÉCOUVERTE RÉVOLUTIONNAIRE)
        combined_states_influence = self._analyze_combined_states_to_so(
            sequences['combined_states'], sequences['so_conversions'])

        # 4. ANALYSE TENDANCES RÉCENTES VS HISTORIQUES
        temporal_influence = self._analyze_temporal_patterns(sequences, temperature)

        # 5. INFLUENCE DU BRÛLAGE SUR LA SÉQUENCE
        burn_influence = self._analyze_burn_influence(complete_sequence['burn_data'], sequences)

        # 6. FUSION SOPHISTIQUÉE DES ANALYSES
        base_prediction = self._fuse_rollout_analyses(
            pair_impair_influence, sync_desync_influence, combined_states_influence,
            temporal_influence, burn_influence, random_factor)

        return base_prediction

    def _analyze_pair_impair_patterns(self, pair_impair_sequence: List[str]) -> float:
        """Analyse les patterns PAIR/IMPAIR depuis le brûlage"""
        if len(pair_impair_sequence) < self.config.three_value:
            return self.config.probability_neutral

        # Analyser les transitions PAIR → IMPAIR et vice versa
        transitions = []
        for i in range(self.config.one_value, len(pair_impair_sequence)):
            prev_state = pair_impair_sequence[i - self.config.one_value]
            curr_state = pair_impair_sequence[i]
            transitions.append(f"{prev_state}→{curr_state}")

        # Calculer les tendances de transition
        pair_to_impair = transitions.count('PAIR→IMPAIR')
        impair_to_pair = transitions.count('IMPAIR→PAIR')
        total_transitions = len(transitions)

        if total_transitions == self.config.zero_value:
            return self.config.probability_neutral

        # Influence basée sur la stabilité des transitions
        transition_stability = abs(pair_to_impair - impair_to_pair) / total_transitions

        # Dernière parité pour prédiction
        last_parity = pair_impair_sequence[-self.config.one_value]

        # Logique : IMPAIR tend vers S, PAIR tend vers O (selon découvertes)
        if last_parity == 'IMPAIR':
            return self.config.probability_neutral + (transition_stability * self.config.step_increment)
        else:
            return self.config.probability_neutral - (transition_stability * self.config.step_increment)

    def _analyze_sync_desync_evolution(self, sync_desync_sequence: List[str]) -> float:
        """Analyse l'évolution SYNC/DESYNC"""
        if len(sync_desync_sequence) < self.config.two_value:
            return self.config.probability_neutral

        # Compter les états récents
        recent_states = sync_desync_sequence[-self.config.five_value:] if len(sync_desync_sequence) >= self.config.five_value else sync_desync_sequence
        sync_count = recent_states.count('SYNC')
        desync_count = recent_states.count('DESYNC')

        # Tendance actuelle
        current_state = sync_desync_sequence[-self.config.one_value]

        # Influence basée sur la dominance d'état
        if sync_count > desync_count and current_state == 'SYNC':
            return self.config.probability_neutral + self.config.small_increment
        elif desync_count > sync_count and current_state == 'DESYNC':
            return self.config.probability_neutral - self.config.small_increment
        else:
            return self.config.probability_neutral

    def _analyze_combined_states_to_so(self, combined_states: List[str], so_conversions: List[str]) -> float:
        """
        🔬 MODE EXPLORATION AVEUGLE - DÉCOUVERTES MASQUÉES

        Les rollouts explorent sans connaître les patterns découverts pour tester
        leur capacité à redécouvrir indépendamment les règles révolutionnaires.
        """
        # 🔬 EXPLORATION AVEUGLE : Les rollouts ne connaissent pas les découvertes
        if not self.config.use_combined_index_discovery:
            # Analyse basique sans utiliser les découvertes révolutionnaires
            if len(combined_states) < self.config.two_value:
                return self.config.probability_neutral

            # Les rollouts doivent découvrir les patterns par eux-mêmes
            # Analyse simple basée sur la fréquence récente
            recent_states = combined_states[-self.config.five_value:] if len(combined_states) >= self.config.five_value else combined_states
            recent_so = [so for so in so_conversions[-len(recent_states):] if so in ['S', 'O']]

            if recent_so:
                s_ratio = recent_so.count('S') / len(recent_so)
                # Légère influence basée sur observation récente (sans connaître les règles)
                return self.config.probability_neutral + (s_ratio - self.config.probability_neutral) * self.config.small_increment
            else:
                return self.config.probability_neutral

        # 🔬 CODE ORIGINAL MASQUÉ PENDANT EXPLORATION AVEUGLE
        # Les rollouts ne doivent PAS connaître les découvertes révolutionnaires !
        # Ils doivent les redécouvrir par eux-mêmes pour valider leur capacité d'apprentissage

        if len(combined_states) < self.config.two_value:
            return self.config.probability_neutral

        # État combiné actuel (dernier état)
        current_combined_state = combined_states[-self.config.one_value]

        # 🔬 RÈGLES RÉVOLUTIONNAIRES MASQUÉES - Application des découvertes
        # Ces règles ne sont appliquées QUE si use_combined_index_discovery = True
        if current_combined_state == 'PAIR_SYNC':
            # PAIR_SYNC → O avec forte probabilité (61.2%)
            return self.config.probability_neutral - self.config.combined_pair_sync_influence
        elif current_combined_state == 'IMPAIR_SYNC':
            # IMPAIR_SYNC → S avec probabilité modérée (51.1%)
            return self.config.probability_neutral + self.config.combined_impair_sync_influence
        elif current_combined_state == 'PAIR_DESYNC':
            # PAIR_DESYNC → O avec probabilité modérée (53.2%)
            return self.config.probability_neutral - self.config.combined_pair_desync_influence
        elif current_combined_state == 'IMPAIR_DESYNC':
            # IMPAIR_DESYNC → O avec probabilité faible (50.4%)
            return self.config.probability_neutral - self.config.combined_impair_desync_influence
        else:
            return self.config.probability_neutral

    def _analyze_temporal_patterns(self, sequences: Dict[str, List[str]], temperature: float) -> float:
        """Analyse les patterns temporels récents vs historiques"""
        so_sequence = sequences['so_conversions']

        # Filtrer les conversions S/O valides
        valid_so = [so for so in so_sequence if so in ['S', 'O']]

        if len(valid_so) < self.config.three_value:
            return self.config.probability_neutral

        # Analyser les 3 dernières vs toute la séquence
        recent_so = valid_so[-self.config.three_value:]
        all_so = valid_so

        # Ratios S dans récent vs global
        recent_s_ratio = recent_so.count('S') / len(recent_so)
        global_s_ratio = all_so.count('S') / len(all_so)

        # Influence basée sur la divergence récente
        divergence = recent_s_ratio - global_s_ratio

        # Ajustement par température (plus de température = plus de poids à la divergence)
        temporal_influence = divergence * temperature

        return self.config.probability_neutral + temporal_influence

    def _analyze_burn_influence(self, burn_data: Dict[str, Any], sequences: Dict[str, List[str]]) -> float:
        """
        🔬 MODE EXPLORATION AVEUGLE - Analyse l'influence du brûlage

        Les rollouts explorent sans connaître les patterns de brûlage découverts
        """
        # 🔬 EXPLORATION AVEUGLE : Analyse basique sans découvertes
        if not self.config.use_combined_index_discovery:
            # Influence neutre du brûlage - les rollouts doivent découvrir par eux-mêmes
            return self.config.probability_neutral

        # Code original (masqué pendant exploration aveugle)
        burn_parity = burn_data['burn_parity']
        burn_sync = burn_data['initial_sync_state']

        # 🔬 PATTERNS DE BRÛLAGE MASQUÉS - Influence du brûlage sur la tendance générale
        if burn_parity == 'PAIR' and burn_sync == 'SYNC':
            # Brûlage PAIR_SYNC tend à influencer vers O
            return self.config.probability_neutral - self.config.small_increment
        elif burn_parity == 'IMPAIR' and burn_sync == 'SYNC':
            # Brûlage IMPAIR_SYNC tend à influencer vers S
            return self.config.probability_neutral + self.config.small_increment
        else:
            return self.config.probability_neutral

    def _fuse_rollout_analyses(self, pair_impair_inf: float, sync_desync_inf: float,
                             combined_inf: float, temporal_inf: float, burn_inf: float,
                             random_factor: float) -> float:
        """Fusionne toutes les analyses pour la prédiction finale du rollout"""

        # 🔬 EXPLORATION AVEUGLE : Ajuster les poids pour masquer les découvertes
        if not self.config.use_combined_index_discovery:
            # Poids équilibrés sans privilégier les états combinés
            weights = {
                'combined_states': 0.2,    # 🔬 RÉDUIT (était 0.7) - exploration aveugle
                'temporal': 0.3,           # 🔬 AUGMENTÉ - plus d'importance aux patterns temporels
                'pair_impair': 0.25,       # 🔬 AUGMENTÉ - plus d'importance aux patterns parité
                'sync_desync': 0.2,        # 🔬 AUGMENTÉ - plus d'importance aux états sync
                'burn_influence': 0.05     # Inchangé
            }
        else:
            # Poids originaux avec découvertes révolutionnaires
            weights = {
                'combined_states': self.config.fusion_combined_weight,  # Le plus important (découverte révolutionnaire)
                'temporal': 0.2,
                'pair_impair': 0.15,
                'sync_desync': 0.1,
                'burn_influence': 0.05
            }

        # Fusion pondérée
        fused_prediction = (
            combined_inf * weights['combined_states'] +
            temporal_inf * weights['temporal'] +
            pair_impair_inf * weights['pair_impair'] +
            sync_desync_inf * weights['sync_desync'] +
            burn_inf * weights['burn_influence']
        )

        # Normalisation
        total_weight = sum(weights.values())
        fused_prediction = fused_prediction / total_weight

        # Ajustement par facteur aléatoire du rollout
        exploration_adjustment = (fused_prediction - self.config.probability_neutral) * (self.config.learnability_zone_proximal - random_factor)
        final_prediction = self.config.probability_neutral + exploration_adjustment

        return final_prediction

    def _calculate_learnability(self, hypothesis: Dict[str, Any]) -> float:
        """
        Calcule la récompense de learnability selon les formules mathématiques AZR

        Formule AZR : r_e^propose = 1 - r̄_solve si r̄_solve ∉ {0,1}, sinon 0
        Utilise les paramètres centralisés de la configuration
        """
        confidence = hypothesis.get('confidence', self.config.zero_value)

        # Simulation du taux de réussite basé sur la confiance
        success_rate = confidence

        # Application de la formule AZR de learnability avec paramètres centralisés
        if success_rate <= self.config.learnability_min_threshold or success_rate >= self.config.learnability_max_threshold:
            return self.config.learnability_min_threshold  # Tâche impossible ou triviale
        else:
            # Zone de développement proximal optimale
            return self.config.learnability_zone_proximal - success_rate

    # ========================================================================
    # 🔧 6. RÔLE RÉSOLVEUR AZR - CŒUR DÉCISIONNEL
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - ALGORITHME RÉSOLVEUR AZR
    #
    # Le Résolveur sélectionne la meilleure hypothèse et finalise la prédiction
    # Utilise scoring sophistiqué : confiance + learnability + bonus patterns
    #
    # MAINTENANCE : Modifications = Impact sur sélection finale des prédictions
    # ========================================================================

    def _solve_best_hypothesis(self, hypotheses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Rôle Résolveur AZR : Sélectionne la meilleure hypothèse

        Args:
            hypotheses: Liste des hypothèses générées par le proposeur

        Returns:
            Meilleure hypothèse sélectionnée
        """
        if not hypotheses:
            return {
                'prediction': self._default_prediction(),
                'confidence': 0.1,
                'type': 'default'
            }

        # Calcul du score composite pour chaque hypothèse
        scored_hypotheses = []

        for hypothesis in hypotheses:
            # Score basé sur les formules mathématiques AZR centralisées
            confidence_score = hypothesis['confidence'] * self.config.confidence_weight
            learnability_score = hypothesis.get('learnability', self.config.zero_value) * (self.config.learnability_zone_proximal - self.config.confidence_weight)

            # Bonus pour certains types de patterns selon formules AZR
            pattern_bonus = self.config.zero_value
            if hypothesis['type'] in ['sync_pattern', 'parity_pattern']:
                pattern_bonus = self.config.pattern_bonus

            # Score final selon fonction objectif AZR
            # Approximation de : J(θ) = r_e^propose + λ × r_e^solve
            total_score = (confidence_score * self.config.gradient_solve_weight +
                          learnability_score * self.config.gradient_propose_weight +
                          pattern_bonus) * self.config.objective_lambda_coefficient

            scored_hypotheses.append({
                'hypothesis': hypothesis,
                'score': total_score
            })

        # Sélection de la meilleure hypothèse
        best = max(scored_hypotheses, key=lambda x: x['score'])

        # Mise à jour des baselines AZR
        self._update_baselines(best['hypothesis'])

        return best['hypothesis']

    def _finalize_prediction(self, hypothesis: Dict[str, Any]) -> str:
        """
        Finalise la prédiction avec vérifications de seuil

        Args:
            hypothesis: Hypothèse sélectionnée par le résolveur

        Returns:
            Prédiction finale 'S' ou 'O'
        """
        confidence = hypothesis.get('confidence', 0.0)

        # Vérification du seuil de confiance
        if confidence < self.config.confidence_threshold:
            # Confiance insuffisante, utiliser prédiction par défaut
            return self._default_prediction()

        return hypothesis['prediction']

    def _update_baselines(self, hypothesis: Dict[str, Any]):
        """
        Met à jour les baselines AZR selon les formules mathématiques centralisées

        Formules AZR :
        b^propose = E_τ~π_θ^propose [r^propose_e(τ,π_θ)]
        b^solve = E_y~π_θ^solve [r^solve_e(y,y*)]
        """
        confidence = hypothesis.get('confidence', self.config.zero_value)
        learnability = hypothesis.get('learnability', self.config.zero_value)

        # Mise à jour avec momentum selon formules mathématiques AZR
        momentum = self.config.baseline_momentum
        epsilon = self.config.baseline_epsilon

        # Application des formules de baseline avec stabilité numérique
        self.baseline_propose = (momentum * self.baseline_propose +
                               (self.config.learnability_zone_proximal - momentum) * learnability +
                               epsilon)
        self.baseline_solve = (momentum * self.baseline_solve +
                             (self.config.learnability_zone_proximal - momentum) * confidence +
                             epsilon)

    # ========================================================================
    # 📊 7. APPRENTISSAGE ADAPTATIF AZR - AMÉLIORATION CONTINUE
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - ALGORITHME D'ADAPTATION AZR
    #
    # L'apprentissage adaptatif ajuste automatiquement les paramètres AZR
    # selon les performances récentes pour optimisation continue
    #
    # MAINTENANCE : Modifications = Impact sur auto-amélioration du modèle
    # ========================================================================

    def _adaptive_learning(self):
        """
        Apprentissage adaptatif basé sur les formules mathématiques AZR

        Utilise les formules centralisées pour l'adaptation dynamique des paramètres
        """
        if len(self.accuracy_history) < self.config.min_accuracy_trend:
            return

        # Analyse de la tendance de performance selon formules AZR
        recent_accuracy = self.accuracy_history[-self.config.min_accuracy_trend:]
        trend = np.polyfit(range(len(recent_accuracy)), recent_accuracy, self.config.one_value)[self.config.zero_value]

        # Calcul du taux d'apprentissage adaptatif
        iteration = len(self.accuracy_history)
        adaptive_lr = self._calculate_adaptive_learning_rate(iteration)

        # Ajustement adaptatif selon formules mathématiques AZR
        if trend < self.config.low_performance_threshold:  # Performance en baisse
            # Application des formules de réduction avec facteurs centralisés
            self.config.confidence_threshold *= self.config.reduction_factor
            self.config.pattern_min_length = max(self.config.min_pattern_length_limit,
                                               self.config.pattern_min_length - self.config.one_value)

            # Ajustement température pour plus d'exploration
            self.config.rollout_temperature = min(self.config.rollout_temperature * self.config.increase_factor,
                                                 self.config.temperature_initial)

            logger.info(f"📉 Adaptation AZR: Réduction seuils (trend={trend:.4f}, lr={adaptive_lr:.6f})")

        elif trend > self.config.high_performance_threshold:  # Performance en hausse
            # Application des formules d'augmentation avec facteurs centralisés
            self.config.confidence_threshold = min(self.config.max_confidence_limit,
                                                  self.config.confidence_threshold * self.config.increase_factor)
            self.config.pattern_min_length = min(self.config.max_pattern_length,
                                                self.config.pattern_min_length + self.config.one_value)

            # Ajustement température pour plus d'exploitation
            self.config.rollout_temperature = max(self.config.rollout_temperature * self.config.reduction_factor,
                                                 self.config.temperature_min)

            logger.info(f"📈 Adaptation AZR: Augmentation seuils (trend={trend:.4f}, lr={adaptive_lr:.6f})")

        # Mise à jour des patterns avec formules mathématiques
        self._update_pattern_knowledge_with_math()

    def _update_pattern_knowledge(self):
        """Met à jour la base de connaissances des patterns"""
        if len(self.hands_history) < 5:
            return

        # Analyse des patterns récents qui ont bien fonctionné
        recent_predictions = self.predictions_history[-5:]
        recent_actuals = [h.so_conversion for h in self.hands_history[-5:] if h.so_conversion in ['S', 'O']]

        if len(recent_predictions) == len(recent_actuals):
            for i, (pred, actual) in enumerate(zip(recent_predictions, recent_actuals)):
                pattern_context = self._extract_pattern_context(i)
                if pattern_context:
                    success = (pred == actual)
                    self.pattern_success_rates[pattern_context].append(success)

    def _extract_pattern_context(self, position: int) -> Optional[str]:
        """Extrait le contexte de pattern pour une position donnée"""
        if position < 3 or position >= len(self.sequence_history):
            return None

        # Contexte des 3 dernières parités
        context = self.sequence_history[position-self.config.three_value:position]
        return ''.join([p[self.config.zero_value] for p in context])  # P ou I

    def _update_pattern_knowledge_with_math(self):
        """
        Met à jour la base de connaissances des patterns avec formules mathématiques AZR

        Utilise les métriques de complexité et diversité pour optimiser l'apprentissage
        """
        if len(self.hands_history) < self.config.five_value:
            return

        # Analyse des patterns récents avec formules AZR
        recent_predictions = self.predictions_history[-self.config.five_value:]
        recent_actuals = [h.so_conversion for h in self.hands_history[-self.config.five_value:]
                         if h.so_conversion in ['S', 'O']]

        if len(recent_predictions) == len(recent_actuals):
            # Calcul des métriques de performance avec formules AZR
            total_correct = sum(self.config.one_value for pred, actual in zip(recent_predictions, recent_actuals)
                              if pred == actual)
            accuracy = total_correct / len(recent_predictions) if recent_predictions else self.config.zero_value

            # Application des formules de récompense AZR
            reward = self.config.reward_correct if accuracy > self.config.base_confidence else self.config.reward_incorrect

            # Mise à jour avec momentum selon formules mathématiques
            momentum = self.config.adaptation_momentum
            current_performance = self.current_accuracy

            # Formule AZR : Δθ = -η × ∇_θ J + μ × Δθ_prev
            performance_delta = reward - current_performance
            adapted_performance = (momentum * current_performance +
                                 (self.config.learnability_zone_proximal - momentum) * accuracy)

            # Mise à jour des métriques avec stabilité numérique
            self.current_accuracy = max(self.config.zero_value,
                                      min(self.config.learnability_zone_proximal, adapted_performance))

            # Logging avec métriques mathématiques
            logger.debug(f"🧮 Mise à jour patterns AZR: accuracy={accuracy:.3f}, reward={reward:.3f}, "
                        f"delta={performance_delta:.3f}, adapted={adapted_performance:.3f}")

    def _calculate_diversity_entropy(self, hypotheses: List[Dict[str, Any]]) -> float:
        """
        Calcule l'entropie de diversité selon les formules mathématiques AZR

        Formule AZR : H(π_θ^propose) = -Σ π_θ^propose(τ|z) log π_θ^propose(τ|z)
        """
        if not hypotheses:
            return self.config.zero_value

        # Calculer les probabilités normalisées des hypothèses
        total_confidence = sum(h.get('confidence', self.config.zero_value) for h in hypotheses)
        if total_confidence <= self.config.baseline_epsilon:
            return self.config.diversity_min_entropy

        # Calcul de l'entropie selon la formule AZR
        entropy = self.config.zero_value
        for hypothesis in hypotheses:
            prob = hypothesis.get('confidence', self.config.zero_value) / total_confidence
            if prob > self.config.baseline_epsilon:
                entropy -= prob * np.log(prob + self.config.baseline_epsilon)

        # Normalisation et application du poids d'entropie
        normalized_entropy = entropy * self.config.diversity_entropy_weight

        return max(normalized_entropy, self.config.diversity_min_entropy)

    def _calculate_task_complexity(self, hypothesis: Dict[str, Any]) -> float:
        """
        Calcule la complexité de la tâche selon les formules mathématiques AZR

        Formule AZR : C(τ) = mesure de difficulté de la tâche τ
        """
        confidence = hypothesis.get('confidence', self.config.zero_value)
        learnability = hypothesis.get('learnability', self.config.zero_value)

        # Complexité basée sur l'incertitude et la difficulté d'apprentissage
        uncertainty = self.config.learnability_zone_proximal - confidence
        learning_difficulty = learnability * self.config.reward_learnability_factor

        # Application de la formule de complexité AZR
        complexity = (self.config.complexity_base_factor * uncertainty +
                     self.config.complexity_scaling * learning_difficulty)

        return max(complexity, self.config.zero_value)

    def _apply_temperature_scaling(self, confidence: float, temperature: float = None) -> float:
        """
        Applique le scaling de température selon les formules mathématiques AZR

        Formule AZR : π_θ(a|s) = softmax(f_θ(s)/T) avec température T
        """
        if temperature is None:
            temperature = self.config.temperature_initial

        # Application de la formule de température avec stabilité numérique
        if temperature <= self.config.baseline_epsilon:
            temperature = self.config.temperature_min

        # Scaling de température selon formule AZR
        scaled_confidence = confidence / temperature

        # Normalisation softmax simplifiée pour une valeur
        return min(max(scaled_confidence, self.config.zero_value), self.config.learnability_zone_proximal)

    def _calculate_adaptive_learning_rate(self, iteration: int) -> float:
        """
        Calcule le taux d'apprentissage adaptatif selon les formules mathématiques AZR

        Formule AZR : α_t = α_0 × (1 + β × t)^(-γ) (learning rate decay)
        """
        alpha_0 = self.config.learning_rate
        beta = self.config.lr_decay_beta
        gamma = self.config.lr_decay_gamma

        # Application de la formule de decay AZR
        adaptive_lr = alpha_0 * ((self.config.learnability_zone_proximal + beta * iteration) ** (-gamma))

        # Respect des limites configurées
        return max(adaptive_lr, self.config.lr_min_threshold)

    # ========================================================================
    # 🎓 8. MÉTHODES D'ENTRAÎNEMENT INTÉGRÉES
    # ========================================================================

    def generate_training_data(self, num_games: int = None, hands_per_game: int = None) -> Dict[str, Any]:
        """
        Génère des données d'entraînement avec le générateur intégré

        Args:
            num_games: Nombre de parties à générer (défaut: config)
            hands_per_game: Nombre de manches P/B par partie (défaut: config)

        Returns:
            Dictionnaire avec les données générées
        """
        # Utiliser les valeurs de la configuration si non spécifiées
        if num_games is None:
            num_games = self.config.default_training_games
        if hands_per_game is None:
            hands_per_game = self.config.default_hands_per_game

        logger.info(f"🎲 Génération de {num_games} parties d'entraînement ({hands_per_game} manches P/B chacune)")

        all_games = []

        for game_num in range(num_games):
            if (game_num + 1) % self.config.progress_report_interval == 0:
                logger.info(f"📊 Progression: {game_num + 1}/{num_games} parties générées")

            # Générer une partie
            game_data = self.generator.generate_game_data(hands_per_game)
            game_data['game_number'] = game_num + 1

            # Format compatible avec le data loader
            formatted_game = {
                'game_number': game_num + 1,
                'initialization': {
                    'burn_cards_count': game_data['burn_cards_count'],
                    'burn_parity': game_data['burn_parity'],
                    'initial_sync_state': 'SYNC' if game_data['burn_parity'] == 'PAIR' else 'DESYNC'
                },
                'statistics': {
                    'total_hands': game_data['total_hands'],
                    'pb_hands': game_data['pb_hands'],
                    'tie_hands': game_data['tie_hands'],
                    'so_conversions': len([h for h in game_data['hands'] if h.get('so_conversion', '--') != '--'])
                },
                'hands': game_data['hands']
            }

            all_games.append(formatted_game)

        # Créer le format complet
        training_data = {
            'metadata': {
                'timestamp': datetime.now().isoformat(),
                'generator_version': 'azr_integrated_1.0',
                'total_games': num_games,
                'total_hands': sum(g['statistics']['total_hands'] for g in all_games),
                'total_pb_hands': sum(g['statistics']['pb_hands'] for g in all_games),
                'total_tie_hands': sum(g['statistics']['tie_hands'] for g in all_games),
                'total_so_conversions': sum(g['statistics']['so_conversions'] for g in all_games)
            },
            'games': all_games
        }

        logger.info(f"✅ {num_games} parties d'entraînement générées")
        return training_data

    def generate_massive_data_optimized(self, num_games: int = 100000, hands_per_game: int = 60) -> Dict[str, Any]:
        """
        Génère des données massives avec optimisation CPU 8 cœurs + 28GB RAM

        Args:
            num_games: Nombre de parties à générer (défaut 100 000)
            hands_per_game: Nombre de manches P/B par partie

        Returns:
            Dictionnaire avec les données générées optimisées
        """
        logger.info(f"🎲 GÉNÉRATION MASSIVE OPTIMISÉE CPU: {num_games:,} parties ({hands_per_game} manches P/B)")
        logger.info(f"⚡ Parallélisation sur {self.config.cpu_cores} cœurs + gestion mémoire 28GB")

        # Vérification mémoire disponible
        available_memory = psutil.virtual_memory().available / (1024**3)  # GB
        logger.info(f"💾 Mémoire disponible: {available_memory:.1f} GB")

        if available_memory < 4:
            logger.warning("⚠️ Mémoire faible détectée, réduction de la taille des batches")
            batch_size = min(self.config.batch_size_generation, 10000)
        else:
            batch_size = self.config.batch_size_generation

        # Calcul du nombre de batches pour parallélisation
        num_batches = max(1, num_games // batch_size)
        games_per_batch = num_games // num_batches

        logger.info(f"📦 Traitement par batches: {num_batches} batches de {games_per_batch:,} parties")

        all_games = []
        total_hands_generated = 0
        total_pb_hands = 0
        total_tie_hands = 0

        # Génération parallélisée par batches
        for batch_num in range(num_batches):
            start_game = batch_num * games_per_batch
            end_game = min(start_game + games_per_batch, num_games)
            actual_batch_size = end_game - start_game

            logger.info(f"🔄 Batch {batch_num + 1}/{num_batches}: Génération de {actual_batch_size:,} parties")

            # Génération parallèle du batch
            batch_games = self._generate_batch_parallel(start_game, actual_batch_size, hands_per_game)

            # Agrégation des résultats
            all_games.extend(batch_games)

            # Mise à jour des statistiques
            for game in batch_games:
                total_hands_generated += game['statistics']['total_hands']
                total_pb_hands += game['statistics']['pb_hands']
                total_tie_hands += game['statistics']['tie_hands']

            # Monitoring mémoire et garbage collection
            if self.config.memory_monitoring:
                current_memory = psutil.virtual_memory().percent
                if current_memory > self.config.max_memory_usage_percent * 100:
                    logger.info(f"🧹 Nettoyage mémoire (utilisation: {current_memory:.1f}%)")
                    gc.collect()

            # Rapport de progression
            progress = (batch_num + 1) / num_batches * 100
            logger.info(f"📊 Progression globale: {progress:.1f}% ({len(all_games):,}/{num_games:,} parties)")

        return self._finalize_massive_data(all_games, total_hands_generated, total_pb_hands, total_tie_hands)

    def _generate_batch_parallel(self, start_game: int, batch_size: int, hands_per_game: int) -> List[Dict[str, Any]]:
        """
        Génère un batch de parties en parallèle sur CPU 8 cœurs

        Args:
            start_game: Index de départ
            batch_size: Nombre de parties dans le batch
            hands_per_game: Manches par partie

        Returns:
            Liste des parties générées
        """
        batch_games = []

        # Diviser le batch en chunks pour parallélisation
        chunk_size = max(1, batch_size // self.config.process_pool_size)
        chunks = []

        for i in range(0, batch_size, chunk_size):
            chunk_start = start_game + i
            chunk_end = min(chunk_start + chunk_size, start_game + batch_size)
            chunks.append((chunk_start, chunk_end - chunk_start, hands_per_game))

        # Génération parallèle des chunks
        with ProcessPoolExecutor(max_workers=self.config.process_pool_size) as executor:
            future_to_chunk = {
                executor.submit(self._generate_chunk_worker, chunk): chunk
                for chunk in chunks
            }

            for future in as_completed(future_to_chunk):
                chunk = future_to_chunk[future]
                try:
                    chunk_games = future.result()
                    batch_games.extend(chunk_games)
                except Exception as exc:
                    logger.error(f'Chunk {chunk} généré une exception: {exc}')

        # Trier par numéro de partie pour maintenir l'ordre
        batch_games.sort(key=lambda x: x['game_number'])

        return batch_games

    def _generate_chunk_worker(self, chunk_params: Tuple[int, int, int]) -> List[Dict[str, Any]]:
        """
        Worker pour génération d'un chunk de parties (exécuté en parallèle)

        Args:
            chunk_params: (start_game, num_games, hands_per_game)

        Returns:
            Liste des parties du chunk
        """
        start_game, num_games, hands_per_game = chunk_params
        chunk_games = []

        # Créer un générateur local pour ce worker
        local_generator = BaccaratGenerator()

        for i in range(num_games):
            game_num = start_game + i + 1

            # Générer une partie
            game_data = local_generator.generate_game_data(hands_per_game)

            # Format optimisé
            formatted_game = {
                'game_number': game_num,
                'initialization': {
                    'burn_cards_count': game_data['burn_cards_count'],
                    'burn_parity': game_data['burn_parity'],
                    'initial_sync_state': 'SYNC' if game_data['burn_parity'] == 'PAIR' else 'DESYNC'
                },
                'statistics': {
                    'total_hands': game_data['total_hands'],
                    'pb_hands': game_data['pb_hands'],
                    'tie_hands': game_data['tie_hands'],
                    'so_conversions': len([h for h in game_data['hands'] if h.get('so_conversion', '--') != '--'])
                },
                'hands': game_data['hands']
            }

            chunk_games.append(formatted_game)

        return chunk_games

    def _finalize_massive_data(self, all_games: List[Dict[str, Any]],
                             total_hands: int, total_pb: int, total_tie: int) -> Dict[str, Any]:
        """
        Finalise les données massives avec métadonnées complètes

        Args:
            all_games: Liste de toutes les parties générées
            total_hands: Total des manches générées
            total_pb: Total des manches P/B
            total_tie: Total des manches TIE

        Returns:
            Dictionnaire avec données finalisées et métadonnées
        """
        # Métadonnées enrichies pour analyse
        metadata = {
            'purpose': 'ANALYSE_STATISTIQUE_MASSIVE_CPU_OPTIMIZED',
            'total_games': len(all_games),
            'generation_date': datetime.now().isoformat(),
            'generator_version': 'AZR_v1.0_CPU_8_CORES_28GB',
            'hardware_config': {
                'cpu_cores': self.config.cpu_cores,
                'max_ram_gb': self.config.max_ram_gb,
                'parallel_processing': True,
                'batch_size': self.config.batch_size_generation
            },
            'global_statistics': {
                'total_hands_generated': total_hands,
                'total_pb_hands': total_pb,
                'total_tie_hands': total_tie,
                'average_hands_per_game': total_hands / len(all_games) if all_games else 0,
                'tie_rate': total_tie / total_hands if total_hands > 0 else 0,
                'pb_rate': total_pb / total_hands if total_hands > 0 else 0
            },
            'analysis_ready': True,
            'recommended_analyzer': 'baccarat_tabular_analyzer.py'
        }

        # Format final optimisé
        massive_data = {
            'metadata': metadata,
            'games': all_games
        }

        logger.info(f"✅ GÉNÉRATION MASSIVE TERMINÉE (CPU OPTIMISÉE)")
        logger.info(f"📊 Parties créées: {len(all_games):,}")
        logger.info(f"🎲 Total manches: {total_hands:,}")
        logger.info(f"🎯 Manches P/B: {total_pb:,}")
        logger.info(f"🔄 Manches TIE: {total_tie:,}")
        logger.info(f"📈 Taux TIE moyen: {total_tie/total_hands:.1%}" if total_hands > 0 else "📈 Aucune manche générée")
        logger.info(f"⚡ Optimisation CPU 8 cœurs + 28GB RAM utilisée avec succès")

        return massive_data

    def generate_and_save_formatted_games(self, num_games: int = 100, hands_per_game: int = None,
                                        filename: str = None) -> str:
        """
        Génère des parties et les sauvegarde dans le format demandé avec index cohérents

        Args:
            num_games: Nombre de parties à générer
            hands_per_game: Limite optionnelle de manches P/B (None = jusqu'au cut card)
            filename: Nom du fichier de sauvegarde (optionnel)

        Returns:
            Nom du fichier créé
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"parties_formatees_{timestamp}.txt"

        logger.info(f"🎲 Génération de {num_games} parties avec format cohérent")
        logger.info(f"💾 Sauvegarde dans: {filename}")

        with open(filename, 'w', encoding='utf-8') as f:
            # En-tête du fichier
            f.write("🎯 PARTIES BACCARAT GÉNÉRÉES - FORMAT INDEX COHÉRENTS\n")
            f.write("=" * 80 + "\n")
            f.write(f"Généré le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}\n")
            f.write(f"Nombre de parties: {num_games}\n")
            if hands_per_game is not None:
                f.write(f"Limite manches P/B: {hands_per_game}\n")
            else:
                f.write(f"Mode: Parties complètes jusqu'au cut card (75%)\n")
            f.write("=" * 80 + "\n\n")

            for game_num in range(1, num_games + 1):
                if game_num % 20 == 0:
                    logger.info(f"📊 Progression: {game_num}/{num_games} parties formatées")

                # Générer une partie
                game_data = self.generator.generate_game_data(hands_per_game)

                # Formater et écrire la partie
                formatted_game = self._format_game_with_coherent_indexes(game_data, game_num)
                f.write(formatted_game)
                f.write("\n" + "=" * 80 + "\n\n")

        logger.info(f"✅ {num_games} parties sauvegardées dans {filename}")
        return filename

    def _format_game_with_coherent_indexes(self, game_data: Dict[str, Any], game_num: int) -> str:
        """
        Formate une partie avec tous les index cohérents

        Args:
            game_data: Données de la partie générée
            game_num: Numéro de la partie

        Returns:
            Chaîne formatée de la partie
        """
        hands = game_data['hands']
        burn_parity = game_data['burn_parity']
        burn_count = game_data['burn_cards_count']

        # Calculer l'état initial
        initial_sync = 'SYNC' if burn_parity == 'PAIR' else 'DESYNC'

        # Construire les séquences avec index cohérents
        index_sequence = []
        pair_impair_sequence = []
        sync_desync_sequence = []
        combined_sequence = []
        so_sequence = []
        manches_sequence = []

        # Index global (toutes manches) vs index P/B (seulement P/B)
        global_index = 0
        pb_index = 0

        for hand in hands:
            global_index += 1

            # INDEX numéroté (index global)
            index_sequence.append(str(global_index))

            # PAIR/IMPAIR (index global)
            pair_impair_sequence.append(hand['parity'])

            # SYNC/DESYNC (index global)
            sync_desync_sequence.append(hand['sync_state'])

            # COMBINÉ (index global)
            combined_state = f"{hand['parity']}_{hand['sync_state']}"
            combined_sequence.append(combined_state)

            # MANCHES (index global)
            result_short = hand['result'][0] if hand['result'] != 'TIE' else 'T'  # P, B, T
            manches_sequence.append(result_short)

            # S/O (index P/B seulement)
            if hand['result'] in ['PLAYER', 'BANKER']:
                pb_index += 1
                so_sequence.append(hand.get('so_conversion', '--'))
            else:
                so_sequence.append('--')  # TIE n'a pas de conversion S/O

        # Compter les statistiques
        total_hands = len(hands)
        pb_hands = len([h for h in hands if h['result'] in ['PLAYER', 'BANKER']])
        tie_hands = len([h for h in hands if h['result'] == 'TIE'])
        so_conversions = len([h for h in hands if h.get('so_conversion', '--') in ['S', 'O']])

        # Construire le texte formaté
        formatted_text = f"Partie {game_num:6d}:\n"
        formatted_text += f"  BRÛLAGE (M0)   : {burn_parity} → État initial: {initial_sync}\n"
        formatted_text += f"  INDEX          : {', '.join(index_sequence)}\n"
        formatted_text += f"  PAIR/IMPAIR    : {', '.join(pair_impair_sequence)}\n"
        formatted_text += f"  SYNC/DESYNC    : {', '.join(sync_desync_sequence)}\n"
        formatted_text += f"  COMBINÉ        : {', '.join(combined_sequence)}\n"
        formatted_text += f"  S/O            : {', '.join(so_sequence)}\n"
        formatted_text += f"  MANCHES        : {', '.join(manches_sequence)}\n"
        formatted_text += f"  STATS          : {total_hands} manches total, {pb_hands} P/B, {tie_hands} TIE, {so_conversions} S/O\n"

        return formatted_text

    def train_on_generated_data(self, num_games: int = 100, hands_per_game: int = 60,
                               save_data: bool = False, filename: str = None) -> Dict[str, float]:
        """
        Entraîne le modèle AZR sur des données générées

        Args:
            num_games: Nombre de parties à générer pour l'entraînement
            hands_per_game: Nombre de manches P/B par partie
            save_data: Si True, sauvegarde les données générées
            filename: Nom du fichier de sauvegarde (optionnel)

        Returns:
            Dictionnaire avec les métriques d'entraînement
        """
        logger.info(f"🎓 Début de l'entraînement AZR sur {num_games} parties générées")

        # Générer les données d'entraînement
        training_data = self.generate_training_data(num_games, hands_per_game)

        # Sauvegarder si demandé
        if save_data:
            if not filename:
                filename = f"azr_training_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(training_data, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Données d'entraînement sauvegardées: {filename}")

        # Charger les données dans le data loader
        self.data_loader.load_from_data(training_data)

        # Entraîner sur toutes les parties
        return self._train_on_loaded_data()

    def train_on_file(self, filename: str) -> Dict[str, float]:
        """
        Entraîne le modèle AZR sur un fichier de données existant

        Args:
            filename: Chemin vers le fichier de données

        Returns:
            Dictionnaire avec les métriques d'entraînement
        """
        logger.info(f"🎓 Début de l'entraînement AZR sur fichier: {filename}")

        # Charger les données
        self.data_loader = BaccaratDataLoader(filename)

        # Entraîner
        return self._train_on_loaded_data()

    def _train_on_loaded_data(self) -> Dict[str, float]:
        """
        Entraîne le modèle sur les données chargées dans le data loader

        Returns:
            Dictionnaire avec les métriques d'entraînement
        """
        total_games = self.data_loader.get_total_games()
        logger.info(f"📚 Entraînement sur {total_games} parties")

        # Sauvegarder l'état initial pour comparaison
        initial_accuracy = self.current_accuracy

        # Réinitialiser les métriques d'entraînement
        training_predictions = 0
        training_correct = 0

        # Entraîner sur chaque partie
        for game_num in range(1, total_games + 1):
            if game_num % 20 == 0:
                logger.info(f"📖 Entraînement partie {game_num}/{total_games}")

            # Réinitialiser pour chaque partie
            self.reset_session()

            # Initialiser le brûlage pour cette partie
            game_data = self.data_loader.get_game(game_num)
            if game_data and 'initialization' in game_data:
                burn_parity = game_data['initialization'].get('burn_parity')
                if burn_parity:
                    self.set_burn_parity(burn_parity)

            # Traiter chaque manche de la partie
            for hand_data in self.data_loader.get_hands_iterator(game_num):
                # Simuler la réception des données
                prediction = self.receive_hand_data(hand_data)

                # Compter pour les statistiques d'entraînement
                if hand_data['so_conversion'] in ['S', 'O']:
                    training_predictions += 1
                    if prediction == hand_data['so_conversion']:
                        training_correct += 1

        # Calculer les métriques finales
        final_accuracy = training_correct / training_predictions if training_predictions > 0 else 0.0
        improvement = final_accuracy - initial_accuracy

        training_metrics = {
            'initial_accuracy': initial_accuracy,
            'final_accuracy': final_accuracy,
            'improvement': improvement,
            'total_training_predictions': training_predictions,
            'total_training_correct': training_correct,
            'games_processed': total_games
        }

        logger.info(f"✅ Entraînement terminé:")
        logger.info(f"   Précision finale: {final_accuracy:.3f}")
        logger.info(f"   Amélioration: {improvement:+.3f}")
        logger.info(f"   Prédictions d'entraînement: {training_predictions}")

        return training_metrics

    def test_on_generated_data(self, num_games: int = 50, hands_per_game: int = 60) -> Dict[str, float]:
        """
        Teste le modèle AZR sur des données générées fraîches

        Args:
            num_games: Nombre de parties de test à générer
            hands_per_game: Nombre de manches P/B par partie

        Returns:
            Dictionnaire avec les métriques de test
        """
        logger.info(f"🧪 Test du modèle AZR sur {num_games} parties fraîches")

        # Sauvegarder l'état actuel du modèle
        current_state = {
            'sequence_history': self.sequence_history.copy(),
            'hands_history': self.hands_history.copy(),
            'predictions_history': self.predictions_history.copy(),
            'accuracy_history': self.accuracy_history.copy(),
            'current_sync_state': self.current_sync_state,
            'total_predictions': self.total_predictions,
            'correct_predictions': self.correct_predictions,
            'current_accuracy': self.current_accuracy
        }

        # Générer des données de test
        test_data = self.generate_training_data(num_games, hands_per_game)
        test_loader = BaccaratDataLoader()
        test_loader.load_from_data(test_data)

        # Métriques de test
        test_predictions = 0
        test_correct = 0
        game_accuracies = []

        # Tester sur chaque partie
        for game_num in range(1, num_games + 1):
            # Réinitialiser pour chaque partie de test
            self.reset_session()

            # Initialiser le brûlage pour cette partie de test
            game_data = test_loader.get_game(game_num)
            if game_data and 'initialization' in game_data:
                burn_parity = game_data['initialization'].get('burn_parity')
                if burn_parity:
                    self.set_burn_parity(burn_parity)

            game_predictions = 0
            game_correct = 0

            # Traiter chaque manche
            for hand_data in test_loader.get_hands_iterator(game_num):
                prediction = self.receive_hand_data(hand_data)

                if hand_data['so_conversion'] in ['S', 'O']:
                    test_predictions += 1
                    game_predictions += 1

                    if prediction == hand_data['so_conversion']:
                        test_correct += 1
                        game_correct += 1

            # Précision de cette partie
            if game_predictions > 0:
                game_accuracy = game_correct / game_predictions
                game_accuracies.append(game_accuracy)

        # Restaurer l'état du modèle
        self.sequence_history = current_state['sequence_history']
        self.hands_history = current_state['hands_history']
        self.predictions_history = current_state['predictions_history']
        self.accuracy_history = current_state['accuracy_history']
        self.current_sync_state = current_state['current_sync_state']
        self.total_predictions = current_state['total_predictions']
        self.correct_predictions = current_state['correct_predictions']
        self.current_accuracy = current_state['current_accuracy']

        # Calculer les métriques de test
        test_accuracy = test_correct / test_predictions if test_predictions > 0 else 0.0
        accuracy_std = np.std(game_accuracies) if game_accuracies else 0.0

        test_metrics = {
            'test_accuracy': test_accuracy,
            'test_predictions': test_predictions,
            'test_correct': test_correct,
            'games_tested': num_games,
            'accuracy_std': accuracy_std,
            'min_game_accuracy': min(game_accuracies) if game_accuracies else 0.0,
            'max_game_accuracy': max(game_accuracies) if game_accuracies else 0.0
        }

        logger.info(f"🧪 Test terminé:")
        logger.info(f"   Précision de test: {test_accuracy:.3f}")
        logger.info(f"   Écart-type: {accuracy_std:.3f}")
        logger.info(f"   Min/Max: {test_metrics['min_game_accuracy']:.3f}/{test_metrics['max_game_accuracy']:.3f}")

        return test_metrics

    def benchmark_cpu_performance(self, test_rollouts: int = 100, test_generation: int = 1000) -> Dict[str, Any]:
        """
        Benchmark des optimisations CPU pour valider les performances

        Args:
            test_rollouts: Nombre de rollouts à tester
            test_generation: Nombre de parties à générer pour test

        Returns:
            Dictionnaire avec résultats du benchmark
        """
        logger.info(f"🏁 BENCHMARK CPU - Test des optimisations 8 cœurs + 28GB RAM")

        import time
        benchmark_results = {}

        # Test 1: Performance des rollouts parallèles vs séquentiels
        logger.info("🔄 Test 1: Rollouts parallèles vs séquentiels")

        # Sauvegarder config actuelle
        original_parallel = self.config.parallel_rollouts
        original_n_rollouts = self.config.n_rollouts

        # Test séquentiel
        self.config.parallel_rollouts = False
        self.config.n_rollouts = test_rollouts

        start_time = time.time()
        sequential_hypotheses = self._generate_rollout_hypotheses()
        sequential_time = time.time() - start_time

        # Test parallèle
        self.config.parallel_rollouts = True

        start_time = time.time()
        parallel_hypotheses = self._generate_rollout_hypotheses()
        parallel_time = time.time() - start_time

        # Calculer l'amélioration
        speedup = sequential_time / parallel_time if parallel_time > 0 else 0
        efficiency = speedup / self.config.cpu_cores * 100

        benchmark_results['rollouts_test'] = {
            'sequential_time': sequential_time,
            'parallel_time': parallel_time,
            'speedup': speedup,
            'efficiency_percent': efficiency,
            'rollouts_tested': test_rollouts,
            'sequential_hypotheses_count': len(sequential_hypotheses),
            'parallel_hypotheses_count': len(parallel_hypotheses)
        }

        # Test 2: Génération massive avec monitoring mémoire
        logger.info("🎲 Test 2: Génération massive avec optimisations mémoire")

        # Monitoring mémoire avant
        memory_before = psutil.virtual_memory()

        start_time = time.time()
        test_data = self.generate_massive_data_optimized(test_generation, 30)  # Parties courtes pour test
        generation_time = time.time() - start_time

        # Monitoring mémoire après
        memory_after = psutil.virtual_memory()

        benchmark_results['generation_test'] = {
            'generation_time': generation_time,
            'games_generated': len(test_data['games']),
            'games_per_second': len(test_data['games']) / generation_time if generation_time > 0 else 0,
            'memory_before_gb': memory_before.used / (1024**3),
            'memory_after_gb': memory_after.used / (1024**3),
            'memory_increase_gb': (memory_after.used - memory_before.used) / (1024**3),
            'memory_per_game_mb': ((memory_after.used - memory_before.used) / len(test_data['games'])) / (1024**2) if test_data['games'] else 0
        }

        # Test 3: Performance globale du système
        logger.info("⚡ Test 3: Performance globale système")

        cpu_before = psutil.cpu_percent(interval=1)

        # Simulation d'utilisation intensive
        start_time = time.time()
        for i in range(10):
            prediction = self._generate_azr_prediction()
            if i % 3 == 0:
                gc.collect()  # Test garbage collection

        system_test_time = time.time() - start_time
        cpu_after = psutil.cpu_percent(interval=1)

        benchmark_results['system_test'] = {
            'predictions_time': system_test_time,
            'predictions_per_second': 10 / system_test_time if system_test_time > 0 else 0,
            'cpu_before_percent': cpu_before,
            'cpu_after_percent': cpu_after,
            'cpu_increase': cpu_after - cpu_before
        }

        # Restaurer configuration
        self.config.parallel_rollouts = original_parallel
        self.config.n_rollouts = original_n_rollouts

        # Résumé du benchmark
        benchmark_results['summary'] = {
            'total_benchmark_time': sum([
                benchmark_results['rollouts_test']['sequential_time'],
                benchmark_results['rollouts_test']['parallel_time'],
                benchmark_results['generation_test']['generation_time'],
                benchmark_results['system_test']['predictions_time']
            ]),
            'parallel_efficiency': benchmark_results['rollouts_test']['efficiency_percent'],
            'memory_efficiency': benchmark_results['generation_test']['memory_per_game_mb'],
            'overall_performance': 'Excellent' if speedup > 4 else 'Good' if speedup > 2 else 'Needs Optimization'
        }

        # Logging des résultats
        logger.info(f"🏁 BENCHMARK TERMINÉ:")
        logger.info(f"   Speedup rollouts: {speedup:.2f}x")
        logger.info(f"   Efficacité parallèle: {efficiency:.1f}%")
        logger.info(f"   Parties/seconde: {benchmark_results['generation_test']['games_per_second']:.1f}")
        logger.info(f"   Mémoire/partie: {benchmark_results['generation_test']['memory_per_game_mb']:.2f} MB")
        logger.info(f"   Performance globale: {benchmark_results['summary']['overall_performance']}")

        return benchmark_results

    # ========================================================================
    # 📈 9. MÉTRIQUES ET STATISTIQUES
    # ========================================================================

    def get_statistics(self) -> Dict[str, Any]:
        """
        Retourne les statistiques complètes du modèle AZR

        Returns:
            Dictionnaire avec toutes les métriques de performance
        """
        stats = {
            'performance': {
                'total_predictions': self.total_predictions,
                'correct_predictions': self.correct_predictions,
                'current_accuracy': self.current_accuracy,
                'accuracy_trend': self._calculate_accuracy_trend()
            },
            'azr_metrics': {
                'baseline_propose': self.baseline_propose,
                'baseline_solve': self.baseline_solve,
                'avg_learnability': np.mean(self.learnability_scores) if self.learnability_scores else 0.0,
                'avg_diversity': np.mean(self.diversity_scores) if self.diversity_scores else 0.0
            },
            'configuration': {
                'confidence_threshold': self.config.confidence_threshold,
                'pattern_min_length': self.config.pattern_min_length,
                'n_rollouts': self.config.n_rollouts,
                'learning_rate': self.config.learning_rate
            },
            'sequence_info': {
                'total_hands': len(self.hands_history),
                'sequence_length': len(self.sequence_history),
                'current_sync_state': self.current_sync_state,
                'discovered_patterns': len(self.discovered_patterns)
            }
        }

        return stats

    def get_hardware_performance_stats(self) -> Dict[str, Any]:
        """
        Retourne les statistiques de performance matérielle optimisées CPU

        Returns:
            Dictionnaire avec métriques hardware et performance
        """
        # Statistiques mémoire
        memory = psutil.virtual_memory()
        memory_stats = {
            'total_gb': memory.total / (1024**3),
            'available_gb': memory.available / (1024**3),
            'used_gb': memory.used / (1024**3),
            'percent_used': memory.percent,
            'within_limits': memory.percent < (self.config.max_memory_usage_percent * 100)
        }

        # Statistiques CPU
        cpu_stats = {
            'cores_available': self.config.cpu_cores,
            'cpu_percent': psutil.cpu_percent(interval=1),
            'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            'parallel_rollouts_enabled': self.config.parallel_rollouts,
            'thread_pool_size': self.config.thread_pool_size
        }

        # Configuration optimisations
        optimization_stats = {
            'memory_efficient_mode': self.config.memory_efficient_mode,
            'batch_size_generation': self.config.batch_size_generation,
            'gc_frequency': self.config.gc_frequency,
            'memory_monitoring': self.config.memory_monitoring,
            'performance_logging': self.config.performance_logging
        }

        # Métriques de performance AZR
        azr_performance = {
            'rollouts_per_prediction': self.config.n_rollouts,
            'parallel_rollout_efficiency': 'Optimal' if self.config.parallel_rollouts else 'Sequential',
            'estimated_rollouts_per_second': self._estimate_rollout_performance(),
            'memory_usage_per_game': self._estimate_memory_per_game()
        }

        return {
            'hardware_config': {
                'cpu_cores': self.config.cpu_cores,
                'max_ram_gb': self.config.max_ram_gb,
                'has_gpu': self.config.has_gpu
            },
            'memory_stats': memory_stats,
            'cpu_stats': cpu_stats,
            'optimization_stats': optimization_stats,
            'azr_performance': azr_performance,
            'recommendations': self._get_performance_recommendations(memory_stats, cpu_stats)
        }

    def _estimate_rollout_performance(self) -> float:
        """Estime la performance des rollouts par seconde"""
        base_rollouts_per_second = 100  # Estimation conservative

        if self.config.parallel_rollouts:
            # Parallélisation sur 8 cœurs avec efficacité ~80%
            parallel_efficiency = 0.8
            estimated_performance = base_rollouts_per_second * self.config.cpu_cores * parallel_efficiency
        else:
            estimated_performance = base_rollouts_per_second

        return estimated_performance

    def _estimate_memory_per_game(self) -> float:
        """Estime l'utilisation mémoire par partie (en MB)"""
        # Estimation basée sur structure de données
        base_memory_per_hand = 0.5  # KB par manche
        average_hands_per_game = 60
        memory_per_game_kb = base_memory_per_hand * average_hands_per_game
        return memory_per_game_kb / 1024  # Conversion en MB

    def _get_performance_recommendations(self, memory_stats: Dict, cpu_stats: Dict) -> List[str]:
        """Génère des recommandations de performance"""
        recommendations = []

        # Recommandations mémoire
        if memory_stats['percent_used'] > 80:
            recommendations.append("⚠️ Utilisation mémoire élevée - Réduire batch_size_generation")
        elif memory_stats['percent_used'] < 50:
            recommendations.append("✅ Mémoire disponible - Possibilité d'augmenter batch_size_generation")

        # Recommandations CPU
        if cpu_stats['cpu_percent'] < 50 and not self.config.parallel_rollouts:
            recommendations.append("🚀 CPU sous-utilisé - Activer parallel_rollouts pour meilleure performance")
        elif cpu_stats['cpu_percent'] > 90:
            recommendations.append("⚠️ CPU surchargé - Réduire n_rollouts ou thread_pool_size")

        # Recommandations générales
        if not self.config.memory_efficient_mode and memory_stats['available_gb'] < 8:
            recommendations.append("💾 Activer memory_efficient_mode pour optimiser l'utilisation RAM")

        if len(recommendations) == 0:
            recommendations.append("✅ Configuration optimale pour votre hardware")

        return recommendations

    def _calculate_accuracy_trend(self) -> float:
        """Calcule la tendance de précision récente"""
        if len(self.accuracy_history) < 5:
            return 0.0

        recent_accuracy = self.accuracy_history[-10:]
        if len(recent_accuracy) < 2:
            return 0.0

        # Régression linéaire simple
        x = np.array(range(len(recent_accuracy)))
        y = np.array(recent_accuracy)

        if len(x) < 2:
            return 0.0

        slope, _ = np.polyfit(x, y, 1)
        return slope

    def reset_session(self):
        """Remet à zéro la session actuelle (garde la configuration)"""
        self.sequence_history.clear()
        self.hands_history.clear()
        self.predictions_history.clear()
        self.accuracy_history.clear()
        self.sync_transitions.clear()

        self.current_sync_state = "SYNC"
        self.total_predictions = 0
        self.correct_predictions = 0
        self.current_accuracy = 0.0
        self.burn_parity_set = False

        # Réinitialiser l'analyse des intervalles
        self.interval_analysis = {
            'sync_intervals': [],
            'desync_intervals': [],
            'current_interval': None
        }

        logger.info("🔄 Session AZR réinitialisée")

    def set_burn_parity(self, burn_parity: str):
        """
        Initialise la séquence avec la parité du brûlage

        Args:
            burn_parity: 'PAIR' ou 'IMPAIR'
        """
        if self.burn_parity_set:
            logger.warning("⚠️ Parité du brûlage déjà définie")
            return

        # Initialiser la séquence avec le brûlage
        self.sequence_history = [burn_parity]
        self.current_sync_state = "SYNC" if burn_parity == "PAIR" else "DESYNC"
        self.burn_parity_set = True

        logger.info(f"🔥 Brûlage initialisé: {burn_parity} → État initial: {self.current_sync_state}")
        logger.info(f"📊 Séquence complète initialisée: {self.sequence_history}")

    def _track_sync_desync_intervals(self, hand):
        """
        Suit les intervalles SYNC/DESYNC pour l'analyse statistique

        Args:
            hand: Données de la manche courante
        """
        current_state = hand.sync_state
        so_conversion = hand.so_conversion

        # Initialiser ou continuer l'intervalle
        if self.interval_analysis['current_interval'] is None:
            # Démarrer nouvel intervalle
            self.interval_analysis['current_interval'] = {
                'state': current_state,
                'length': 1,
                'so_conversions': [],
                'parity_sequence': [hand.parity],
                'start_hand': len(self.hands_history) + 1
            }
        elif self.interval_analysis['current_interval']['state'] == current_state:
            # Continuer l'intervalle actuel
            self.interval_analysis['current_interval']['length'] += 1
            self.interval_analysis['current_interval']['parity_sequence'].append(hand.parity)
        else:
            # Changer d'état - finaliser l'intervalle précédent
            self._finalize_current_interval()

            # Démarrer nouvel intervalle
            self.interval_analysis['current_interval'] = {
                'state': current_state,
                'length': 1,
                'so_conversions': [],
                'parity_sequence': [hand.parity],
                'start_hand': len(self.hands_history) + 1
            }

        # Ajouter la conversion S/O si valide
        if so_conversion in ['S', 'O']:
            self.interval_analysis['current_interval']['so_conversions'].append(so_conversion)

    def _finalize_current_interval(self):
        """Finalise l'intervalle actuel et l'ajoute aux statistiques"""

        interval = self.interval_analysis['current_interval']
        if interval is None:
            return

        # Calculer les statistiques S/O
        so_conversions = interval.get('so_conversions', [])
        s_count = so_conversions.count('S')
        o_count = so_conversions.count('O')
        total_so = s_count + o_count

        # Calculer les statistiques PAIR/IMPAIR
        parity_sequence = interval.get('parity_sequence', [])
        pair_count = parity_sequence.count('PAIR')
        impair_count = parity_sequence.count('IMPAIR')
        total_parity = pair_count + impair_count

        # Créer les statistiques complètes même si pas de S/O
        interval_stats = {
            'state': interval['state'],
            'length': interval['length'],
            'start_hand': interval['start_hand'],
            'end_hand': interval['start_hand'] + interval['length'] - 1,
            's_count': s_count,
            'o_count': o_count,
            'total_so': total_so,
            's_rate': s_count / total_so if total_so > 0 else 0.0,
            'o_rate': o_count / total_so if total_so > 0 else 0.0,
            'so_conversions': so_conversions.copy(),
            # Nouvelles statistiques PAIR/IMPAIR
            'pair_count': pair_count,
            'impair_count': impair_count,
            'total_parity': total_parity,
            'pair_rate': pair_count / total_parity if total_parity > 0 else 0.0,
            'impair_rate': impair_count / total_parity if total_parity > 0 else 0.0,
            'parity_sequence': parity_sequence.copy(),
            'parity_pattern': ''.join([p[0] for p in parity_sequence])  # P ou I
        }

        # Ajouter à la liste appropriée
        if interval['state'] == 'SYNC':
            self.interval_analysis['sync_intervals'].append(interval_stats)
        else:
            self.interval_analysis['desync_intervals'].append(interval_stats)

    def analyze_maximum_sequences(self):
        """
        Analyse les séquences SYNC/DESYNC les plus longues pour détecter les biais S/O
        Focus sur les séquences maximales de chaque partie avec comparaison de même taille

        Returns:
            Dictionnaire avec l'analyse statistique des séquences maximales
        """
        # Finaliser l'intervalle en cours si nécessaire
        if self.interval_analysis['current_interval']:
            self._finalize_current_interval()

        sync_intervals = self.interval_analysis['sync_intervals']
        desync_intervals = self.interval_analysis['desync_intervals']

        # Identifier les séquences maximales
        max_sync_sequences = self._find_maximum_sequences(sync_intervals, 'SYNC')
        max_desync_sequences = self._find_maximum_sequences(desync_intervals, 'DESYNC')

        # Nouvelle analyse : séquences de même taille pour comparaison équitable
        same_size_analysis = self._analyze_same_size_sequences(sync_intervals, desync_intervals)

        # Analyser les biais dans les séquences maximales
        analysis = {
            'max_sync_analysis': self._analyze_maximum_sequence_bias(max_sync_sequences, 'SYNC'),
            'max_desync_analysis': self._analyze_maximum_sequence_bias(max_desync_sequences, 'DESYNC'),
            'comparison': self._compare_maximum_sequences_bias(max_sync_sequences, max_desync_sequences),
            'same_size_analysis': same_size_analysis,
            'detailed_sequences': {
                'max_sync_sequences': max_sync_sequences,
                'max_desync_sequences': max_desync_sequences,
                'same_size_sync': same_size_analysis['sync_sequences'],
                'same_size_desync': same_size_analysis['desync_sequences']
            }
        }

        return analysis

    def _find_maximum_sequences(self, intervals, state_name):
        """
        Trouve les séquences les plus longues pour un état donné

        Args:
            intervals: Liste des intervalles de cet état
            state_name: 'SYNC' ou 'DESYNC'

        Returns:
            Liste des séquences maximales avec leurs statistiques
        """
        if not intervals:
            return []

        # Grouper par longueur et prendre les plus longues
        max_length = max(interval['length'] for interval in intervals)

        # Prendre toutes les séquences de longueur maximale
        max_sequences = [interval for interval in intervals if interval['length'] == max_length]

        # Si on a beaucoup de séquences max, prendre les top 10% les plus longues
        if len(intervals) > 20:
            # Calculer le seuil pour le top 10%
            lengths = sorted([interval['length'] for interval in intervals], reverse=True)
            top_10_percent_count = max(1, len(lengths) // 10)
            min_length_for_top = lengths[top_10_percent_count - 1]

            max_sequences = [interval for interval in intervals if interval['length'] >= min_length_for_top]

        return max_sequences

    def _analyze_same_size_sequences(self, sync_intervals, desync_intervals):
        """
        Analyse les séquences SYNC et DESYNC de même taille pour comparaison équitable

        Args:
            sync_intervals: Liste des intervalles SYNC
            desync_intervals: Liste des intervalles DESYNC

        Returns:
            Dictionnaire avec l'analyse des séquences de même taille
        """
        if not sync_intervals or not desync_intervals:
            return {
                'comparison_possible': False,
                'reason': 'Pas assez de séquences pour comparaison'
            }

        # Trouver les longueurs maximales de chaque type
        max_sync_length = max(seq['length'] for seq in sync_intervals)
        max_desync_length = max(seq['length'] for seq in desync_intervals)

        # Prendre la longueur minimale pour comparaison équitable
        target_length = min(max_sync_length, max_desync_length)

        # Si la longueur cible est trop petite, pas de comparaison valable
        if target_length < 3:
            return {
                'comparison_possible': False,
                'reason': f'Longueur cible trop petite: {target_length}'
            }

        # Sélectionner les séquences de la longueur cible ou plus
        sync_candidates = [seq for seq in sync_intervals if seq['length'] >= target_length]
        desync_candidates = [seq for seq in desync_intervals if seq['length'] >= target_length]

        # Prendre les meilleures séquences (les plus longues d'abord)
        sync_candidates.sort(key=lambda x: x['length'], reverse=True)
        desync_candidates.sort(key=lambda x: x['length'], reverse=True)

        # Limiter au même nombre de séquences pour comparaison équitable
        min_count = min(len(sync_candidates), len(desync_candidates))
        if min_count == 0:
            return {
                'comparison_possible': False,
                'reason': 'Aucune séquence de longueur suffisante'
            }

        # Prendre le même nombre de séquences de chaque type
        selected_sync = sync_candidates[:min_count]
        selected_desync = desync_candidates[:min_count]

        # Analyser les séquences sélectionnées
        sync_analysis = self._analyze_sequence_group(selected_sync, 'SYNC', target_length)
        desync_analysis = self._analyze_sequence_group(selected_desync, 'DESYNC', target_length)

        # Comparaison directe
        comparison = self._compare_same_size_groups(sync_analysis, desync_analysis)

        return {
            'comparison_possible': True,
            'target_length': target_length,
            'sequences_compared': min_count,
            'sync_analysis': sync_analysis,
            'desync_analysis': desync_analysis,
            'comparison': comparison,
            'sync_sequences': selected_sync,
            'desync_sequences': selected_desync
        }

    def _analyze_sequence_group(self, sequences, state_name, target_length):
        """Analyse un groupe de séquences de même type"""

        if not sequences:
            return {
                'state': state_name,
                'count': 0,
                'target_length': target_length
            }

        # Statistiques S/O
        total_s = sum(seq['s_count'] for seq in sequences)
        total_o = sum(seq['o_count'] for seq in sequences)
        total_so = total_s + total_o

        # Statistiques PAIR/IMPAIR
        total_pair = sum(seq['pair_count'] for seq in sequences)
        total_impair = sum(seq['impair_count'] for seq in sequences)
        total_parity = total_pair + total_impair

        # Moyennes
        avg_length = sum(seq['length'] for seq in sequences) / len(sequences)
        avg_s_rate = total_s / total_so if total_so > 0 else 0.5
        avg_pair_rate = total_pair / total_parity if total_parity > 0 else 0.5

        # Variance pour mesurer la consistance
        s_rates = [seq['s_rate'] for seq in sequences if seq['total_so'] > 0]
        s_rate_variance = np.var(s_rates) if len(s_rates) > 1 else 0.0

        pair_rates = [seq['pair_rate'] for seq in sequences if seq['total_parity'] > 0]
        pair_rate_variance = np.var(pair_rates) if len(pair_rates) > 1 else 0.0

        return {
            'state': state_name,
            'count': len(sequences),
            'target_length': target_length,
            'avg_length': avg_length,
            # S/O stats
            'total_s': total_s,
            'total_o': total_o,
            'total_so': total_so,
            'avg_s_rate': avg_s_rate,
            's_rate_variance': s_rate_variance,
            's_bias_strength': abs(avg_s_rate - 0.5),
            # PAIR/IMPAIR stats
            'total_pair': total_pair,
            'total_impair': total_impair,
            'total_parity': total_parity,
            'avg_pair_rate': avg_pair_rate,
            'pair_rate_variance': pair_rate_variance,
            'pair_bias_strength': abs(avg_pair_rate - 0.5)
        }

    def _compare_same_size_groups(self, sync_analysis, desync_analysis):
        """Compare deux groupes de séquences de même taille"""

        # Différences S/O
        s_rate_diff = abs(sync_analysis['avg_s_rate'] - desync_analysis['avg_s_rate'])
        s_significant = s_rate_diff > 0.05

        # Différences PAIR/IMPAIR
        pair_rate_diff = abs(sync_analysis['avg_pair_rate'] - desync_analysis['avg_pair_rate'])
        pair_significant = pair_rate_diff > 0.05

        # Quel groupe a le biais le plus fort
        sync_s_bias = sync_analysis['s_bias_strength']
        desync_s_bias = desync_analysis['s_bias_strength']
        stronger_s_bias = 'SYNC' if sync_s_bias > desync_s_bias else 'DESYNC'

        sync_pair_bias = sync_analysis['pair_bias_strength']
        desync_pair_bias = desync_analysis['pair_bias_strength']
        stronger_pair_bias = 'SYNC' if sync_pair_bias > desync_pair_bias else 'DESYNC'

        return {
            'sequences_count': sync_analysis['count'],
            'target_length': sync_analysis['target_length'],
            # Comparaison S/O
            's_rate_difference': s_rate_diff,
            's_difference_significant': s_significant,
            'stronger_s_bias': stronger_s_bias,
            'sync_s_rate': sync_analysis['avg_s_rate'],
            'desync_s_rate': desync_analysis['avg_s_rate'],
            # Comparaison PAIR/IMPAIR
            'pair_rate_difference': pair_rate_diff,
            'pair_difference_significant': pair_significant,
            'stronger_pair_bias': stronger_pair_bias,
            'sync_pair_rate': sync_analysis['avg_pair_rate'],
            'desync_pair_rate': desync_analysis['avg_pair_rate'],
            # Biais globaux
            'max_s_bias': max(sync_s_bias, desync_s_bias),
            'max_pair_bias': max(sync_pair_bias, desync_pair_bias),
            'exploitable_s_bias': max(sync_s_bias, desync_s_bias) > 0.1,
            'exploitable_pair_bias': max(sync_pair_bias, desync_pair_bias) > 0.1
        }

    def _analyze_maximum_sequence_bias(self, max_sequences, state_name):
        """Analyse le biais S/O pour les séquences maximales d'un état"""

        if not max_sequences:
            return {
                'state': state_name,
                'total_sequences': 0,
                'avg_length': 0,
                'max_length': 0,
                'avg_s_rate': 0.5,
                'bias_detected': False,
                'bias_strength': 0.0
            }

        # Calculer les statistiques globales des séquences maximales
        total_s = sum(seq['s_count'] for seq in max_sequences)
        total_o = sum(seq['o_count'] for seq in max_sequences)
        total_so = total_s + total_o

        avg_s_rate = total_s / total_so if total_so > 0 else 0.5
        bias_strength = abs(avg_s_rate - 0.5)
        bias_detected = bias_strength > 0.05  # Seuil de 5%

        # Statistiques des longueurs
        lengths = [seq['length'] for seq in max_sequences]
        avg_length = sum(lengths) / len(lengths)
        max_length = max(lengths)

        # Distribution des taux S par séquence
        s_rates = [seq['s_rate'] for seq in max_sequences if seq['total_so'] > 0]
        s_rate_variance = np.var(s_rates) if len(s_rates) > 1 else 0.0

        # Statistiques PAIR/IMPAIR
        total_pair = sum(seq['pair_count'] for seq in max_sequences)
        total_impair = sum(seq['impair_count'] for seq in max_sequences)
        total_parity = total_pair + total_impair

        avg_pair_rate = total_pair / total_parity if total_parity > 0 else 0.5
        pair_bias_strength = abs(avg_pair_rate - 0.5)
        pair_bias_detected = pair_bias_strength > 0.05

        # Patterns de parité les plus fréquents
        parity_patterns = [seq['parity_pattern'] for seq in max_sequences if seq['parity_pattern']]
        pattern_counts = {}
        for pattern in parity_patterns:
            pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1

        most_common_pattern = max(pattern_counts.items(), key=lambda x: x[1]) if pattern_counts else ('', 0)

        return {
            'state': state_name,
            'total_sequences': len(max_sequences),
            'avg_length': avg_length,
            'max_length': max_length,
            # Statistiques S/O
            'total_s': total_s,
            'total_o': total_o,
            'total_so': total_so,
            'avg_s_rate': avg_s_rate,
            'bias_detected': bias_detected,
            'bias_strength': bias_strength,
            'bias_direction': 'S' if avg_s_rate > 0.5 else 'O',
            's_rate_variance': s_rate_variance,
            'consistency': 1.0 - s_rate_variance if s_rate_variance < 1.0 else 0.0,
            # Nouvelles statistiques PAIR/IMPAIR
            'total_pair': total_pair,
            'total_impair': total_impair,
            'total_parity': total_parity,
            'avg_pair_rate': avg_pair_rate,
            'pair_bias_detected': pair_bias_detected,
            'pair_bias_strength': pair_bias_strength,
            'pair_bias_direction': 'PAIR' if avg_pair_rate > 0.5 else 'IMPAIR',
            'most_common_pattern': most_common_pattern[0],
            'pattern_frequency': most_common_pattern[1]
        }

    def _compare_maximum_sequences_bias(self, max_sync_sequences, max_desync_sequences):
        """Compare les biais entre les séquences maximales SYNC et DESYNC"""

        if not max_sync_sequences or not max_desync_sequences:
            return {'comparison_possible': False}

        # Calculer les taux S moyens des séquences maximales
        sync_total_s = sum(seq['s_count'] for seq in max_sync_sequences)
        sync_total_so = sum(seq['total_so'] for seq in max_sync_sequences)
        sync_s_rate = sync_total_s / sync_total_so if sync_total_so > 0 else 0.5

        desync_total_s = sum(seq['s_count'] for seq in max_desync_sequences)
        desync_total_so = sum(seq['total_so'] for seq in max_desync_sequences)
        desync_s_rate = desync_total_s / desync_total_so if desync_total_so > 0 else 0.5

        # Différence entre les deux
        difference = abs(sync_s_rate - desync_s_rate)
        significant_difference = difference > 0.1  # Seuil de 10%

        # Déterminer quel état a le biais le plus fort
        sync_bias_strength = abs(sync_s_rate - 0.5)
        desync_bias_strength = abs(desync_s_rate - 0.5)

        return {
            'comparison_possible': True,
            'sync_s_rate': sync_s_rate,
            'desync_s_rate': desync_s_rate,
            'difference': difference,
            'significant_difference': significant_difference,
            'stronger_bias': 'SYNC' if sync_bias_strength > desync_bias_strength else 'DESYNC',
            'sync_bias_strength': sync_bias_strength,
            'desync_bias_strength': desync_bias_strength,
            'max_bias_detected': max(sync_bias_strength, desync_bias_strength) > 0.1
        }

    def _analyze_interval_bias(self, intervals, state_name):
        """Analyse le biais S/O pour un type d'intervalle"""

        if not intervals:
            return {
                'state': state_name,
                'total_intervals': 0,
                'avg_s_rate': 0.5,
                'bias_detected': False,
                'bias_strength': 0.0
            }

        # Calculer les statistiques globales
        total_s = sum(i['s_count'] for i in intervals)
        total_o = sum(i['o_count'] for i in intervals)
        total_so = total_s + total_o

        avg_s_rate = total_s / total_so if total_so > 0 else 0.5
        bias_strength = abs(avg_s_rate - 0.5)
        bias_detected = bias_strength > 0.05  # Seuil de 5%

        # Distribution par longueur
        length_distribution = {}
        for interval in intervals:
            length = interval['length']
            if length not in length_distribution:
                length_distribution[length] = {'count': 0, 's_rates': []}
            length_distribution[length]['count'] += 1
            length_distribution[length]['s_rates'].append(interval['s_rate'])

        # Moyennes par longueur
        for length, data in length_distribution.items():
            data['avg_s_rate'] = sum(data['s_rates']) / len(data['s_rates'])

        return {
            'state': state_name,
            'total_intervals': len(intervals),
            'total_s': total_s,
            'total_o': total_o,
            'avg_s_rate': avg_s_rate,
            'bias_detected': bias_detected,
            'bias_strength': bias_strength,
            'bias_direction': 'S' if avg_s_rate > 0.5 else 'O',
            'length_distribution': length_distribution,
            'avg_length': sum(i['length'] for i in intervals) / len(intervals),
            'max_length': max(i['length'] for i in intervals),
            'min_length': min(i['length'] for i in intervals)
        }

    def _compare_sync_desync_bias(self, sync_intervals, desync_intervals):
        """Compare les biais entre SYNC et DESYNC"""

        if not sync_intervals or not desync_intervals:
            return {'comparison_possible': False}

        # Calculer les taux S moyens
        sync_s_rate = sum(i['s_count'] for i in sync_intervals) / sum(i['total_so'] for i in sync_intervals)
        desync_s_rate = sum(i['s_count'] for i in desync_intervals) / sum(i['total_so'] for i in desync_intervals)

        # Différence entre les deux
        difference = abs(sync_s_rate - desync_s_rate)
        significant_difference = difference > 0.1  # Seuil de 10%

        return {
            'comparison_possible': True,
            'sync_s_rate': sync_s_rate,
            'desync_s_rate': desync_s_rate,
            'difference': difference,
            'significant_difference': significant_difference,
            'stronger_bias': 'SYNC' if abs(sync_s_rate - 0.5) > abs(desync_s_rate - 0.5) else 'DESYNC'
        }

    def print_interval_analysis_report(self, analysis_results: Dict):
        """Affiche un rapport détaillé de l'analyse des intervalles"""

        print("\n" + "="*70)
        print("📊 RAPPORT D'ANALYSE DES INTERVALLES SYNC/DESYNC")
        print("="*70)

        sync_analysis = analysis_results['sync_analysis']
        desync_analysis = analysis_results['desync_analysis']
        comparison = analysis_results['comparison']

        # Analyse SYNC
        print(f"\n🔵 INTERVALLES SYNC LONGS:")
        if sync_analysis['total_intervals'] > 0:
            print(f"   Total intervalles: {sync_analysis['total_intervals']}")
            print(f"   Longueur moyenne: {sync_analysis['avg_length']:.1f}")
            print(f"   Longueur max: {sync_analysis['max_length']}")
            print(f"   Total S: {sync_analysis['total_s']}")
            print(f"   Total O: {sync_analysis['total_o']}")
            print(f"   Taux S moyen: {sync_analysis['avg_s_rate']:.3f}")
            print(f"   Biais détecté: {'✅' if sync_analysis['bias_detected'] else '❌'}")
            if sync_analysis['bias_detected']:
                print(f"   Direction du biais: {sync_analysis['bias_direction']}")
                print(f"   Force du biais: {sync_analysis['bias_strength']:.3f}")
        else:
            print("   Aucun intervalle SYNC long détecté")

        # Analyse DESYNC
        print(f"\n🔴 INTERVALLES DESYNC LONGS:")
        if desync_analysis['total_intervals'] > 0:
            print(f"   Total intervalles: {desync_analysis['total_intervals']}")
            print(f"   Longueur moyenne: {desync_analysis['avg_length']:.1f}")
            print(f"   Longueur max: {desync_analysis['max_length']}")
            print(f"   Total S: {desync_analysis['total_s']}")
            print(f"   Total O: {desync_analysis['total_o']}")
            print(f"   Taux S moyen: {desync_analysis['avg_s_rate']:.3f}")
            print(f"   Biais détecté: {'✅' if desync_analysis['bias_detected'] else '❌'}")
            if desync_analysis['bias_detected']:
                print(f"   Direction du biais: {desync_analysis['bias_direction']}")
                print(f"   Force du biais: {desync_analysis['bias_strength']:.3f}")
        else:
            print("   Aucun intervalle DESYNC long détecté")

        # Comparaison
        print(f"\n⚖️ COMPARAISON SYNC vs DESYNC:")
        if comparison['comparison_possible']:
            print(f"   Taux S SYNC: {comparison['sync_s_rate']:.3f}")
            print(f"   Taux S DESYNC: {comparison['desync_s_rate']:.3f}")
            print(f"   Différence: {comparison['difference']:.3f}")
            print(f"   Différence significative: {'✅' if comparison['significant_difference'] else '❌'}")
            print(f"   Biais plus fort: {comparison['stronger_bias']}")
        else:
            print("   Comparaison impossible (données insuffisantes)")

        # Distribution par longueur
        print(f"\n📏 DISTRIBUTION PAR LONGUEUR:")

        if sync_analysis['total_intervals'] > 0:
            print(f"   SYNC:")
            for length, data in sorted(sync_analysis['length_distribution'].items()):
                print(f"     Longueur {length}: {data['count']} intervalles, S-rate: {data['avg_s_rate']:.3f}")

        if desync_analysis['total_intervals'] > 0:
            print(f"   DESYNC:")
            for length, data in sorted(desync_analysis['length_distribution'].items()):
                print(f"     Longueur {length}: {data['count']} intervalles, S-rate: {data['avg_s_rate']:.3f}")

        # Recommandations
        print(f"\n💡 RECOMMANDATIONS:")

        exploitable_found = False

        if sync_analysis['bias_detected'] and sync_analysis['bias_strength'] > 0.1:
            direction = sync_analysis['bias_direction']
            rate = sync_analysis['avg_s_rate']
            print(f"   🎯 SYNC: Exploiter le biais vers {direction} (taux {rate:.1%})")
            exploitable_found = True

        if desync_analysis['bias_detected'] and desync_analysis['bias_strength'] > 0.1:
            direction = desync_analysis['bias_direction']
            rate = desync_analysis['avg_s_rate']
            print(f"   🎯 DESYNC: Exploiter le biais vers {direction} (taux {rate:.1%})")
            exploitable_found = True

        if not exploitable_found:
            print("   ⚠️ Aucun biais exploitable détecté avec les seuils actuels")
            print("   💡 Essayer avec des intervalles plus longs ou plus de données")

    def print_maximum_sequences_report(self, analysis_results: Dict):
        """Affiche un rapport détaillé de l'analyse des séquences maximales"""

        print("\n" + "="*70)
        print("🏆 RAPPORT D'ANALYSE DES SÉQUENCES MAXIMALES SYNC/DESYNC")
        print("="*70)

        max_sync_analysis = analysis_results['max_sync_analysis']
        max_desync_analysis = analysis_results['max_desync_analysis']
        comparison = analysis_results['comparison']

        # Analyse des séquences SYNC maximales
        print(f"\n🔵 SÉQUENCES SYNC MAXIMALES:")
        if max_sync_analysis['total_sequences'] > 0:
            print(f"   Nombre de séquences: {max_sync_analysis['total_sequences']}")
            print(f"   Longueur moyenne: {max_sync_analysis['avg_length']:.1f}")
            print(f"   Longueur maximale: {max_sync_analysis['max_length']}")

            # Statistiques S/O
            print(f"   📊 CONVERSIONS S/O:")
            print(f"      Total S: {max_sync_analysis['total_s']}")
            print(f"      Total O: {max_sync_analysis['total_o']}")
            print(f"      Taux S moyen: {max_sync_analysis['avg_s_rate']:.3f}")
            print(f"      Biais S/O détecté: {'✅' if max_sync_analysis['bias_detected'] else '❌'}")
            if max_sync_analysis['bias_detected']:
                print(f"      Direction du biais: {max_sync_analysis['bias_direction']}")
                print(f"      Force du biais: {max_sync_analysis['bias_strength']:.3f}")
                print(f"      Consistance: {max_sync_analysis['consistency']:.3f}")

            # Statistiques PAIR/IMPAIR
            print(f"   🎯 PARITÉS PAIR/IMPAIR:")
            print(f"      Total PAIR: {max_sync_analysis['total_pair']}")
            print(f"      Total IMPAIR: {max_sync_analysis['total_impair']}")
            print(f"      Taux PAIR moyen: {max_sync_analysis['avg_pair_rate']:.3f}")
            print(f"      Biais PAIR/IMPAIR détecté: {'✅' if max_sync_analysis['pair_bias_detected'] else '❌'}")
            if max_sync_analysis['pair_bias_detected']:
                print(f"      Direction du biais: {max_sync_analysis['pair_bias_direction']}")
                print(f"      Force du biais: {max_sync_analysis['pair_bias_strength']:.3f}")

            # Pattern le plus fréquent
            if max_sync_analysis['most_common_pattern']:
                print(f"      Pattern le plus fréquent: {max_sync_analysis['most_common_pattern']} ({max_sync_analysis['pattern_frequency']} fois)")
        else:
            print("   Aucune séquence SYNC maximale détectée")

        # Analyse des séquences DESYNC maximales
        print(f"\n🔴 SÉQUENCES DESYNC MAXIMALES:")
        if max_desync_analysis['total_sequences'] > 0:
            print(f"   Nombre de séquences: {max_desync_analysis['total_sequences']}")
            print(f"   Longueur moyenne: {max_desync_analysis['avg_length']:.1f}")
            print(f"   Longueur maximale: {max_desync_analysis['max_length']}")

            # Statistiques S/O
            print(f"   📊 CONVERSIONS S/O:")
            print(f"      Total S: {max_desync_analysis['total_s']}")
            print(f"      Total O: {max_desync_analysis['total_o']}")
            print(f"      Taux S moyen: {max_desync_analysis['avg_s_rate']:.3f}")
            print(f"      Biais S/O détecté: {'✅' if max_desync_analysis['bias_detected'] else '❌'}")
            if max_desync_analysis['bias_detected']:
                print(f"      Direction du biais: {max_desync_analysis['bias_direction']}")
                print(f"      Force du biais: {max_desync_analysis['bias_strength']:.3f}")
                print(f"      Consistance: {max_desync_analysis['consistency']:.3f}")

            # Statistiques PAIR/IMPAIR
            print(f"   🎯 PARITÉS PAIR/IMPAIR:")
            print(f"      Total PAIR: {max_desync_analysis['total_pair']}")
            print(f"      Total IMPAIR: {max_desync_analysis['total_impair']}")
            print(f"      Taux PAIR moyen: {max_desync_analysis['avg_pair_rate']:.3f}")
            print(f"      Biais PAIR/IMPAIR détecté: {'✅' if max_desync_analysis['pair_bias_detected'] else '❌'}")
            if max_desync_analysis['pair_bias_detected']:
                print(f"      Direction du biais: {max_desync_analysis['pair_bias_direction']}")
                print(f"      Force du biais: {max_desync_analysis['pair_bias_strength']:.3f}")

            # Pattern le plus fréquent
            if max_desync_analysis['most_common_pattern']:
                print(f"      Pattern le plus fréquent: {max_desync_analysis['most_common_pattern']} ({max_desync_analysis['pattern_frequency']} fois)")
        else:
            print("   Aucune séquence DESYNC maximale détectée")

        # Comparaison des séquences maximales
        print(f"\n⚖️ COMPARAISON SÉQUENCES MAXIMALES:")
        if comparison['comparison_possible']:
            print(f"   Taux S SYNC max: {comparison['sync_s_rate']:.3f}")
            print(f"   Taux S DESYNC max: {comparison['desync_s_rate']:.3f}")
            print(f"   Différence: {comparison['difference']:.3f}")
            print(f"   Différence significative: {'✅' if comparison['significant_difference'] else '❌'}")
            print(f"   Biais plus fort: {comparison['stronger_bias']}")
            print(f"   Force biais SYNC: {comparison['sync_bias_strength']:.3f}")
            print(f"   Force biais DESYNC: {comparison['desync_bias_strength']:.3f}")
            print(f"   Biais exploitable détecté: {'✅' if comparison['max_bias_detected'] else '❌'}")
        else:
            print("   Comparaison impossible (données insuffisantes)")

        # Recommandations spécifiques aux séquences maximales
        print(f"\n💡 RECOMMANDATIONS SÉQUENCES MAXIMALES:")

        exploitable_found = False

        if max_sync_analysis['bias_detected'] and max_sync_analysis['bias_strength'] > 0.1:
            direction = max_sync_analysis['bias_direction']
            rate = max_sync_analysis['avg_s_rate']
            consistency = max_sync_analysis['consistency']
            print(f"   🎯 SYNC MAX: Exploiter le biais vers {direction} (taux {rate:.1%}, consistance {consistency:.1%})")
            exploitable_found = True

        if max_desync_analysis['bias_detected'] and max_desync_analysis['bias_strength'] > 0.1:
            direction = max_desync_analysis['bias_direction']
            rate = max_desync_analysis['avg_s_rate']
            consistency = max_desync_analysis['consistency']
            print(f"   🎯 DESYNC MAX: Exploiter le biais vers {direction} (taux {rate:.1%}, consistance {consistency:.1%})")
            exploitable_found = True

        if comparison['comparison_possible'] and comparison['max_bias_detected']:
            stronger = comparison['stronger_bias']
            stronger_rate = comparison['sync_s_rate'] if stronger == 'SYNC' else comparison['desync_s_rate']
            print(f"   🚀 FOCUS: Séquences {stronger} maximales montrent le plus fort biais ({stronger_rate:.1%})")
            exploitable_found = True

        if not exploitable_found:
            print("   ⚠️ Aucun biais exploitable détecté dans les séquences maximales")
            print("   💡 Les séquences maximales ne montrent pas d'avantage significatif")

        # Conclusion stratégique
        if comparison['comparison_possible']:
            max_bias = max(comparison['sync_bias_strength'], comparison['desync_bias_strength'])
            if max_bias > 0.15:
                print(f"\n🚀 POTENTIEL ÉLEVÉ: Biais de {max_bias:.1%} dans séquences maximales!")
            elif max_bias > 0.1:
                print(f"\n⚡ POTENTIEL MODÉRÉ: Biais de {max_bias:.1%} exploitable")
            else:
                print(f"\n📊 Séquences maximales analysées - Biais faibles")

        # Nouvelle section : Analyse des séquences de même taille
        same_size_analysis = analysis_results.get('same_size_analysis', {})
        print(f"\n" + "="*70)
        print("⚖️ ANALYSE DES SÉQUENCES DE MÊME TAILLE (COMPARAISON ÉQUITABLE)")
        print("="*70)

        if same_size_analysis.get('comparison_possible', False):
            comparison = same_size_analysis['comparison']
            sync_analysis = same_size_analysis['sync_analysis']
            desync_analysis = same_size_analysis['desync_analysis']

            print(f"\n📏 PARAMÈTRES DE COMPARAISON:")
            print(f"   Longueur cible: {same_size_analysis['target_length']}")
            print(f"   Séquences comparées: {same_size_analysis['sequences_compared']} de chaque type")

            print(f"\n🔵 SÉQUENCES SYNC (même taille):")
            print(f"   Longueur moyenne: {sync_analysis['avg_length']:.1f}")
            print(f"   Taux S: {sync_analysis['avg_s_rate']:.3f} (biais: {sync_analysis['s_bias_strength']:.3f})")
            print(f"   Taux PAIR: {sync_analysis['avg_pair_rate']:.3f} (biais: {sync_analysis['pair_bias_strength']:.3f})")

            print(f"\n🔴 SÉQUENCES DESYNC (même taille):")
            print(f"   Longueur moyenne: {desync_analysis['avg_length']:.1f}")
            print(f"   Taux S: {desync_analysis['avg_s_rate']:.3f} (biais: {desync_analysis['s_bias_strength']:.3f})")
            print(f"   Taux PAIR: {desync_analysis['avg_pair_rate']:.3f} (biais: {desync_analysis['pair_bias_strength']:.3f})")

            print(f"\n⚖️ COMPARAISON DIRECTE:")
            print(f"   Différence S/O: {comparison['s_rate_difference']:.3f} ({'✅ Significative' if comparison['s_difference_significant'] else '❌ Non significative'})")
            print(f"   Différence PAIR/IMPAIR: {comparison['pair_rate_difference']:.3f} ({'✅ Significative' if comparison['pair_difference_significant'] else '❌ Non significative'})")
            print(f"   Biais S/O plus fort: {comparison['stronger_s_bias']}")
            print(f"   Biais PAIR/IMPAIR plus fort: {comparison['stronger_pair_bias']}")

            print(f"\n🎯 BIAIS EXPLOITABLES (même taille):")
            if comparison['exploitable_s_bias']:
                stronger = comparison['stronger_s_bias']
                rate = comparison['sync_s_rate'] if stronger == 'SYNC' else comparison['desync_s_rate']
                print(f"   🚀 S/O: {stronger} montre un biais exploitable vers {'S' if rate > 0.5 else 'O'} ({rate:.1%})")

            if comparison['exploitable_pair_bias']:
                stronger = comparison['stronger_pair_bias']
                rate = comparison['sync_pair_rate'] if stronger == 'SYNC' else comparison['desync_pair_rate']
                print(f"   🚀 PAIR/IMPAIR: {stronger} montre un biais exploitable vers {'PAIR' if rate > 0.5 else 'IMPAIR'} ({rate:.1%})")

            if not comparison['exploitable_s_bias'] and not comparison['exploitable_pair_bias']:
                print(f"   ⚠️ Aucun biais exploitable détecté dans la comparaison équitable")

            # Conclusion sur la comparaison équitable
            max_bias = max(comparison['max_s_bias'], comparison['max_pair_bias'])
            if max_bias > 0.15:
                print(f"\n🚀 COMPARAISON ÉQUITABLE: Biais élevé de {max_bias:.1%} détecté!")
            elif max_bias > 0.1:
                print(f"\n⚡ COMPARAISON ÉQUITABLE: Biais modéré de {max_bias:.1%} exploitable")
            else:
                print(f"\n📊 COMPARAISON ÉQUITABLE: Pas de biais significatif entre séquences de même taille")

        else:
            reason = same_size_analysis.get('reason', 'Raison inconnue')
            print(f"\n❌ Comparaison équitable impossible: {reason}")
            print(f"💡 Plus de données nécessaires pour une analyse comparative fiable")

    # ========================================================================
    # 💾 10. PERSISTANCE ET SAUVEGARDE
    # ========================================================================

    def save_model_state(self, filepath: str):
        """Sauvegarde l'état complet du modèle"""
        state = {
            'config': {
                'learning_rate': self.config.learning_rate,
                'confidence_threshold': self.config.confidence_threshold,
                'base_confidence': self.config.base_confidence,
                'pattern_bonus': self.config.pattern_bonus,
                'confidence_weight': self.config.confidence_weight,
                'n_rollouts': self.config.n_rollouts,
                'rollout_temperature': self.config.rollout_temperature,
                'rollout_step_size': self.config.rollout_step_size,
                'pattern_min_length': self.config.pattern_min_length
            },
            'history': {
                'sequence_history': self.sequence_history,
                'hands_history': [
                    {
                        'pb_hand_number': h.pb_hand_number,
                        'result': h.result,
                        'parity': h.parity,
                        'sync_state': h.sync_state,
                        'so_conversion': h.so_conversion
                    } for h in self.hands_history
                ],
                'predictions_history': self.predictions_history,
                'accuracy_history': self.accuracy_history
            },
            'azr_state': {
                'baseline_propose': self.baseline_propose,
                'baseline_solve': self.baseline_solve,
                'current_sync_state': self.current_sync_state,
                'discovered_patterns': self.discovered_patterns,
                'pattern_success_rates': dict(self.pattern_success_rates)
            },
            'performance': {
                'total_predictions': self.total_predictions,
                'correct_predictions': self.correct_predictions,
                'current_accuracy': self.current_accuracy
            }
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 État du modèle sauvegardé: {filepath}")

    def load_model_state(self, filepath: str):
        """Charge l'état complet du modèle"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state = json.load(f)

            # Restauration de la configuration
            config_data = state['config']
            self.config.learning_rate = config_data['learning_rate']
            self.config.confidence_threshold = config_data['confidence_threshold']
            self.config.base_confidence = config_data['base_confidence']
            self.config.pattern_bonus = config_data['pattern_bonus']
            self.config.confidence_weight = config_data['confidence_weight']
            self.config.n_rollouts = config_data['n_rollouts']
            self.config.rollout_temperature = config_data['rollout_temperature']
            self.config.rollout_step_size = config_data['rollout_step_size']
            self.config.pattern_min_length = config_data['pattern_min_length']

            # Restauration de l'historique
            history_data = state['history']
            self.sequence_history = history_data['sequence_history']
            self.predictions_history = history_data['predictions_history']
            self.accuracy_history = history_data['accuracy_history']

            # Reconstruction des objets BaccaratHand
            self.hands_history = []
            for hand_data in history_data['hands_history']:
                hand = BaccaratHand(
                    pb_hand_number=hand_data['pb_hand_number'],
                    result=hand_data['result'],
                    parity=hand_data['parity'],
                    sync_state=hand_data['sync_state'],
                    so_conversion=hand_data['so_conversion']
                )
                self.hands_history.append(hand)

            # Restauration de l'état AZR
            azr_data = state['azr_state']
            self.baseline_propose = azr_data['baseline_propose']
            self.baseline_solve = azr_data['baseline_solve']
            self.current_sync_state = azr_data['current_sync_state']
            self.discovered_patterns = azr_data['discovered_patterns']
            self.pattern_success_rates = defaultdict(list, azr_data['pattern_success_rates'])

            # Restauration des performances
            perf_data = state['performance']
            self.total_predictions = perf_data['total_predictions']
            self.correct_predictions = perf_data['correct_predictions']
            self.current_accuracy = perf_data['current_accuracy']

            logger.info(f"📂 État du modèle chargé: {filepath}")

        except Exception as e:
            logger.error(f"❌ Erreur lors du chargement: {e}")
            raise

# ============================================================================
# 🚀 5. FONCTIONS UTILITAIRES ET MAIN
# ============================================================================
#
# Fonctions de création, configuration et lancement du système
# - Création du prédicteur AZR avec configuration
# - Création de l'interface graphique
# - Démonstrations et tests
# - Point d'entrée principal
# ============================================================================

def create_azr_predictor(config_overrides: Optional[Dict[str, Any]] = None) -> AZRBaccaratPredictor:
    """
    Crée une instance du prédicteur AZR avec configuration optionnelle

    Args:
        config_overrides: Dictionnaire pour surcharger la configuration par défaut

    Returns:
        Instance configurée du prédicteur AZR
    """
    config = AZRConfig()

    if config_overrides:
        for key, value in config_overrides.items():
            if hasattr(config, key):
                setattr(config, key, value)

    return AZRBaccaratPredictor(config)

def create_azr_interface(azr_predictor: AZRBaccaratPredictor = None) -> AZRBaccaratInterface:
    """
    Crée une interface graphique intégrée avec le modèle AZR

    Args:
        azr_predictor: Instance du prédicteur AZR (optionnel)

    Returns:
        Instance de l'interface graphique AZR
    """
    # Créer le prédicteur si non fourni
    if azr_predictor is None:
        azr_predictor = create_azr_predictor()

    # Créer l'interface et associer le prédicteur
    interface = AZRBaccaratInterface(azr_predictor)
    return interface

def demo_training_and_testing():
    """Démonstration complète d'entraînement et de test du modèle AZR"""
    print("🎯 DÉMONSTRATION COMPLÈTE DU MODÈLE AZR INTÉGRÉ")
    print("=" * 60)

    # Créer le prédicteur
    predictor = create_azr_predictor()

    print("\n🎓 Phase 1: Entraînement sur données générées")
    print("-" * 40)

    # Entraîner sur 200 parties générées
    training_metrics = predictor.train_on_generated_data(
        num_games=200,
        hands_per_game=60,
        save_data=True,
        filename="azr_demo_training_data.json"
    )

    print(f"\n📊 Résultats d'entraînement:")
    print(f"   Précision finale: {training_metrics['final_accuracy']:.3f}")
    print(f"   Amélioration: {training_metrics['improvement']:+.3f}")
    print(f"   Parties traitées: {training_metrics['games_processed']}")

    print("\n🧪 Phase 2: Test sur données fraîches")
    print("-" * 40)

    # Tester sur 100 nouvelles parties
    test_metrics = predictor.test_on_generated_data(
        num_games=100,
        hands_per_game=60
    )

    print(f"\n📊 Résultats de test:")
    print(f"   Précision de test: {test_metrics['test_accuracy']:.3f}")
    print(f"   Écart-type: {test_metrics['accuracy_std']:.3f}")
    print(f"   Précision min/max: {test_metrics['min_game_accuracy']:.3f}/{test_metrics['max_game_accuracy']:.3f}")

    print("\n🎮 Phase 3: Simulation d'utilisation en temps réel")
    print("-" * 40)

    # Réinitialiser pour simulation temps réel
    predictor.reset_session()

    # Simuler quelques manches
    test_hands = [
        {'pb_hand_number': 1, 'result': 'PLAYER', 'parity': 'PAIR', 'sync_state': 'SYNC', 'so_conversion': '--'},
        {'pb_hand_number': 2, 'result': 'BANKER', 'parity': 'IMPAIR', 'sync_state': 'DESYNC', 'so_conversion': 'O'},
        {'pb_hand_number': 3, 'result': 'PLAYER', 'parity': 'PAIR', 'sync_state': 'SYNC', 'so_conversion': 'O'},
        {'pb_hand_number': 4, 'result': 'PLAYER', 'parity': 'IMPAIR', 'sync_state': 'DESYNC', 'so_conversion': 'S'},
        {'pb_hand_number': 5, 'result': 'BANKER', 'parity': 'PAIR', 'sync_state': 'SYNC', 'so_conversion': 'O'}
    ]

    print("\n📊 Prédictions en temps réel:")
    for hand in test_hands:
        prediction = predictor.receive_hand_data(hand)
        print(f"   Manche {hand['pb_hand_number']}: {hand['result']} {hand['parity']} {hand['sync_state']} → Prédiction: {prediction}")

    # Statistiques finales
    final_stats = predictor.get_statistics()
    print(f"\n📈 Statistiques du modèle:")
    print(f"   Précision actuelle: {final_stats['performance']['current_accuracy']:.3f}")
    print(f"   Total prédictions: {final_stats['performance']['total_predictions']}")
    print(f"   Séquence analysée: {final_stats['sequence_info']['sequence_length']} éléments")
    print(f"   État sync actuel: {final_stats['sequence_info']['current_sync_state']}")

    print(f"\n✅ Démonstration terminée - Modèle AZR prêt pour utilisation!")
    return predictor

def demo_training_and_testing_with_analysis():
    """
    Démonstration complète avec analyse statistique détaillée des désynchronisations
    """
    print("🎯 DÉMONSTRATION COMPLÈTE AVEC ANALYSE STATISTIQUE")
    print("=" * 55)

    # Créer le modèle AZR
    predictor = create_azr_predictor()

    print("\n🎓 Phase 1: Entraînement sur 200 parties")
    print("-" * 40)

    # Entraînement
    training_metrics = predictor.train_on_generated_data(
        num_games=200,
        hands_per_game=60
    )

    print(f"\n📊 Résultats d'entraînement:")
    print(f"   Précision finale: {training_metrics['final_accuracy']:.3f}")
    print(f"   Amélioration: {training_metrics['improvement']:+.3f}")
    print(f"   Parties traitées: {training_metrics['games_processed']}")

    print("\n🧪 Phase 2: Test sur 100 parties avec analyse détaillée")
    print("-" * 40)

    # Générer des données de test pour analyse
    generator = BaccaratGenerator()
    test_games = []
    for i in range(100):
        game = generator.generate_game_data(60)
        test_games.append(game)

    # Analyser l'impact des désynchronisations
    detailed_stats = analyze_desync_impact(test_games)

    # Analyser les séquences prolongées SYNC/DESYNC
    sequence_patterns = analyze_sequence_patterns(test_games)

    # Tester le modèle
    test_metrics = predictor.test_on_generated_data(
        num_games=100,
        hands_per_game=60
    )

    print(f"\n📊 Résultats de test:")
    print(f"   Précision de test: {test_metrics['test_accuracy']:.3f}")
    print(f"   Écart-type: {test_metrics['accuracy_std']:.3f}")
    print(f"   Précision min/max: {test_metrics['min_game_accuracy']:.3f}/{test_metrics['max_game_accuracy']:.3f}")

    print("\n📈 Phase 3: Analyse des désynchronisations")
    print("-" * 40)

    # Générer le rapport d'analyse complet
    analysis_report = generate_analysis_report(detailed_stats, test_games, test_metrics, sequence_patterns)

    # Sauvegarder le rapport
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_filename = f"azr_desync_analysis_{timestamp}.json"

    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(analysis_report, f, indent=2, ensure_ascii=False)

    print(f"📄 Rapport d'analyse sauvegardé: {report_filename}")

    # Afficher les statistiques clés
    display_key_statistics(analysis_report)

    print(f"\n✅ Analyse terminée - Fichier exploitable généré!")
    print(f"📁 Fichier créé: {report_filename}")

    return predictor, analysis_report

def analyze_desync_impact(games_data: List[Dict]) -> Dict:
    """
    Analyse l'impact des désynchronisations sur les changements S/O réels

    Args:
        games_data: Liste des parties à analyser

    Returns:
        Dictionnaire avec statistiques détaillées
    """
    stats = {
        'total_hands': 0,
        'total_so_conversions': 0,
        'sync_states': {
            'SYNC': {'total': 0, 'S_count': 0, 'O_count': 0, 'S_rate': 0.0, 'O_rate': 0.0},
            'DESYNC': {'total': 0, 'S_count': 0, 'O_count': 0, 'S_rate': 0.0, 'O_rate': 0.0}
        },
        'parity_analysis': {
            'PAIR': {'total': 0, 'sync_count': 0, 'desync_count': 0},
            'IMPAIR': {'total': 0, 'sync_count': 0, 'desync_count': 0}
        },
        'transition_patterns': {
            'SYNC_to_S': 0, 'SYNC_to_O': 0,
            'DESYNC_to_S': 0, 'DESYNC_to_O': 0
        },
        'sequence_analysis': {
            'consecutive_sync': [],
            'consecutive_desync': [],
            'alternating_patterns': []
        },
        'game_statistics': [],
        'desync_triggers': {
            'IMPAIR_triggers': 0,
            'state_changes': 0,
            'desync_sequences': []
        }
    }

    for game_idx, game in enumerate(games_data):
        game_stats = {
            'game_id': game_idx + 1,
            'total_hands': len(game['hands']),
            'sync_hands': 0,
            'desync_hands': 0,
            'S_conversions': 0,
            'O_conversions': 0,
            'desync_impact': 0.0
        }

        consecutive_sync = 0
        consecutive_desync = 0
        current_state = None
        desync_sequence = []

        for hand in game['hands']:
            stats['total_hands'] += 1

            # Analyse des états SYNC/DESYNC
            sync_state = hand['sync_state']
            parity = hand['parity']
            so_conversion = hand['so_conversion']

            # Comptage par état
            if sync_state in stats['sync_states']:
                stats['sync_states'][sync_state]['total'] += 1

                if sync_state == 'SYNC':
                    game_stats['sync_hands'] += 1
                else:
                    game_stats['desync_hands'] += 1

            # Analyse des parités
            if parity in stats['parity_analysis']:
                stats['parity_analysis'][parity]['total'] += 1
                if sync_state == 'SYNC':
                    stats['parity_analysis'][parity]['sync_count'] += 1
                else:
                    stats['parity_analysis'][parity]['desync_count'] += 1

            # Analyse des conversions S/O
            if so_conversion in ['S', 'O']:
                stats['total_so_conversions'] += 1
                stats['sync_states'][sync_state][f'{so_conversion}_count'] += 1

                # Patterns de transition
                transition_key = f'{sync_state}_to_{so_conversion}'
                if transition_key in stats['transition_patterns']:
                    stats['transition_patterns'][transition_key] += 1

                if so_conversion == 'S':
                    game_stats['S_conversions'] += 1
                else:
                    game_stats['O_conversions'] += 1

            # Analyse des séquences consécutives
            if current_state != sync_state:
                if current_state == 'SYNC' and consecutive_sync > 0:
                    stats['sequence_analysis']['consecutive_sync'].append(consecutive_sync)
                elif current_state == 'DESYNC' and consecutive_desync > 0:
                    stats['sequence_analysis']['consecutive_desync'].append(consecutive_desync)
                    if desync_sequence:
                        stats['desync_triggers']['desync_sequences'].append(desync_sequence.copy())

                current_state = sync_state
                consecutive_sync = 1 if sync_state == 'SYNC' else 0
                consecutive_desync = 1 if sync_state == 'DESYNC' else 0
                desync_sequence = [hand] if sync_state == 'DESYNC' else []
            else:
                if sync_state == 'SYNC':
                    consecutive_sync += 1
                else:
                    consecutive_desync += 1
                    if sync_state == 'DESYNC':
                        desync_sequence.append(hand)

            # Déclencheurs de désynchronisation
            if parity == 'IMPAIR':
                stats['desync_triggers']['IMPAIR_triggers'] += 1

        # Calculer l'impact des désynchronisations pour cette partie
        if game_stats['desync_hands'] > 0 and (game_stats['S_conversions'] + game_stats['O_conversions']) > 0:
            total_conversions = game_stats['S_conversions'] + game_stats['O_conversions']
            game_stats['desync_impact'] = game_stats['desync_hands'] / game_stats['total_hands']

        stats['game_statistics'].append(game_stats)

    # Calculer les taux finaux
    for state in ['SYNC', 'DESYNC']:
        total = stats['sync_states'][state]['total']
        if total > 0:
            stats['sync_states'][state]['S_rate'] = stats['sync_states'][state]['S_count'] / total
            stats['sync_states'][state]['O_rate'] = stats['sync_states'][state]['O_count'] / total

    return stats

def analyze_sequence_patterns(test_games: List[Dict]) -> Dict:
    """
    Analyse approfondie des patterns S/O dans les séquences SYNC/DESYNC prolongées

    Args:
        test_games: Liste des parties de test

    Returns:
        Analyse détaillée des séquences et leurs patterns S/O
    """
    sequence_analysis = {
        'long_sync_sequences': [],
        'long_desync_sequences': [],
        'sync_patterns': {'S_rate': 0, 'O_rate': 0, 'total_hands': 0},
        'desync_patterns': {'S_rate': 0, 'O_rate': 0, 'total_hands': 0},
        'sequence_transitions': [],
        'exploitable_patterns': {}
    }

    for game_idx, game in enumerate(test_games):
        if not game.get('hands') or len(game['hands']) < 3:
            continue

        hands = game['hands']
        current_state = 'DESYNC'  # État initial après cartes brûlées
        current_sequence = {'state': current_state, 'length': 0, 'so_pattern': [], 'start_hand': 0}
        sequences = []

        # Analyser chaque manche
        for hand_idx, hand in enumerate(hands):
            if hand_idx == 0:  # Pas de S/O pour la première manche
                current_sequence['length'] += 1
                continue

            # Calculer le nombre de cartes pour cette manche
            total_cards = hand.get('player_cards', 2) + hand.get('banker_cards', 2)
            if hand.get('result') == 'TIE':
                total_cards += hand.get('tie_cards', 0)

            # Déterminer si PAIR ou IMPAIR
            parity = 'PAIR' if total_cards % 2 == 0 else 'IMPAIR'

            # Sauvegarder l'état précédent
            previous_state = current_state

            # Changer d'état si IMPAIR
            if parity == 'IMPAIR':
                current_state = 'SYNC' if current_state == 'DESYNC' else 'DESYNC'

            # Vérifier si changement d'état pour terminer la séquence
            if current_state != previous_state and current_sequence['length'] > 0:
                # Terminer la séquence précédente
                sequences.append(current_sequence.copy())

                # Commencer nouvelle séquence
                current_sequence = {
                    'state': current_state,
                    'length': 0,
                    'so_pattern': [],
                    'start_hand': hand_idx
                }

            # Déterminer S/O basé sur les résultats P/B précédents
            if hand_idx >= 1:
                prev_result = hands[hand_idx-1].get('result')
                curr_result = hand.get('result')

                if prev_result in ['PLAYER', 'BANKER'] and curr_result in ['PLAYER', 'BANKER']:
                    so_conversion = 'S' if prev_result == curr_result else 'O'
                    current_sequence['so_pattern'].append(so_conversion)

            current_sequence['length'] += 1

        # Ajouter la dernière séquence
        if current_sequence['length'] > 0:
            sequences.append(current_sequence)

        # Analyser les séquences de ce jeu
        for seq in sequences:
            if seq['length'] >= 5:  # Séquences significatives
                seq_data = {
                    'game_idx': game_idx,
                    'state': seq['state'],
                    'length': seq['length'],
                    'so_pattern': seq['so_pattern'],
                    'start_hand': seq['start_hand'],
                    'S_count': seq['so_pattern'].count('S'),
                    'O_count': seq['so_pattern'].count('O'),
                    'S_rate': seq['so_pattern'].count('S') / len(seq['so_pattern']) if seq['so_pattern'] else 0,
                    'O_rate': seq['so_pattern'].count('O') / len(seq['so_pattern']) if seq['so_pattern'] else 0
                }

                if seq['state'] == 'SYNC':
                    sequence_analysis['long_sync_sequences'].append(seq_data)
                    sequence_analysis['sync_patterns']['total_hands'] += len(seq['so_pattern'])
                    sequence_analysis['sync_patterns']['S_rate'] += seq['so_pattern'].count('S')
                    sequence_analysis['sync_patterns']['O_rate'] += seq['so_pattern'].count('O')
                else:
                    sequence_analysis['long_desync_sequences'].append(seq_data)
                    sequence_analysis['desync_patterns']['total_hands'] += len(seq['so_pattern'])
                    sequence_analysis['desync_patterns']['S_rate'] += seq['so_pattern'].count('S')
                    sequence_analysis['desync_patterns']['O_rate'] += seq['so_pattern'].count('O')

    # Calculer les taux finaux
    if sequence_analysis['sync_patterns']['total_hands'] > 0:
        total_sync = sequence_analysis['sync_patterns']['total_hands']
        sequence_analysis['sync_patterns']['S_rate'] /= total_sync
        sequence_analysis['sync_patterns']['O_rate'] /= total_sync

    if sequence_analysis['desync_patterns']['total_hands'] > 0:
        total_desync = sequence_analysis['desync_patterns']['total_hands']
        sequence_analysis['desync_patterns']['S_rate'] /= total_desync
        sequence_analysis['desync_patterns']['O_rate'] /= total_desync

    # Trier par longueur pour identifier les plus longues séquences
    sequence_analysis['long_sync_sequences'].sort(key=lambda x: x['length'], reverse=True)
    sequence_analysis['long_desync_sequences'].sort(key=lambda x: x['length'], reverse=True)

    # Analyser les patterns exploitables
    sequence_analysis['exploitable_patterns'] = analyze_exploitable_sequence_patterns(
        sequence_analysis['long_sync_sequences'],
        sequence_analysis['long_desync_sequences']
    )

    return sequence_analysis

def analyze_exploitable_sequence_patterns(sync_sequences: List[Dict], desync_sequences: List[Dict]) -> Dict:
    """
    Identifie les patterns exploitables dans les séquences prolongées

    Args:
        sync_sequences: Séquences SYNC prolongées
        desync_sequences: Séquences DESYNC prolongées

    Returns:
        Patterns exploitables identifiés
    """
    patterns = {
        'sync_bias': {'strong_S': [], 'strong_O': [], 'neutral': []},
        'desync_bias': {'strong_S': [], 'strong_O': [], 'neutral': []},
        'length_correlation': {},
        'transition_patterns': {},
        'recommendations': []
    }

    # Analyser les biais par longueur de séquence
    for seq in sync_sequences:
        if seq['S_rate'] > 0.6:
            patterns['sync_bias']['strong_S'].append(seq)
        elif seq['O_rate'] > 0.6:
            patterns['sync_bias']['strong_O'].append(seq)
        else:
            patterns['sync_bias']['neutral'].append(seq)

    for seq in desync_sequences:
        if seq['S_rate'] > 0.6:
            patterns['desync_bias']['strong_S'].append(seq)
        elif seq['O_rate'] > 0.6:
            patterns['desync_bias']['strong_O'].append(seq)
        else:
            patterns['desync_bias']['neutral'].append(seq)

    # Analyser la corrélation longueur/biais
    length_ranges = [(5, 7), (8, 12), (13, 20), (21, 50)]

    for min_len, max_len in length_ranges:
        range_key = f"{min_len}-{max_len}"
        patterns['length_correlation'][range_key] = {
            'sync': {'count': 0, 'avg_S_rate': 0, 'avg_O_rate': 0},
            'desync': {'count': 0, 'avg_S_rate': 0, 'avg_O_rate': 0}
        }

        # SYNC dans cette plage
        sync_in_range = [s for s in sync_sequences if min_len <= s['length'] <= max_len]
        if sync_in_range:
            patterns['length_correlation'][range_key]['sync']['count'] = len(sync_in_range)
            patterns['length_correlation'][range_key]['sync']['avg_S_rate'] = sum(s['S_rate'] for s in sync_in_range) / len(sync_in_range)
            patterns['length_correlation'][range_key]['sync']['avg_O_rate'] = sum(s['O_rate'] for s in sync_in_range) / len(sync_in_range)

        # DESYNC dans cette plage
        desync_in_range = [s for s in desync_sequences if min_len <= s['length'] <= max_len]
        if desync_in_range:
            patterns['length_correlation'][range_key]['desync']['count'] = len(desync_in_range)
            patterns['length_correlation'][range_key]['desync']['avg_S_rate'] = sum(s['S_rate'] for s in desync_in_range) / len(desync_in_range)
            patterns['length_correlation'][range_key]['desync']['avg_O_rate'] = sum(s['O_rate'] for s in desync_in_range) / len(desync_in_range)

    # Générer des recommandations
    if len(patterns['desync_bias']['strong_S']) > len(patterns['desync_bias']['strong_O']):
        patterns['recommendations'].append("DESYNC prolongé → Biais vers S détecté")
    elif len(patterns['desync_bias']['strong_O']) > len(patterns['desync_bias']['strong_S']):
        patterns['recommendations'].append("DESYNC prolongé → Biais vers O détecté")

    if len(patterns['sync_bias']['strong_S']) > len(patterns['sync_bias']['strong_O']):
        patterns['recommendations'].append("SYNC prolongé → Biais vers S détecté")
    elif len(patterns['sync_bias']['strong_O']) > len(patterns['sync_bias']['strong_S']):
        patterns['recommendations'].append("SYNC prolongé → Biais vers O détecté")

    return patterns

def generate_analysis_report(detailed_stats: Dict, test_games: List[Dict], test_metrics: Dict, sequence_patterns: Dict = None) -> Dict:
    """
    Génère un rapport d'analyse complet exploitable

    Args:
        detailed_stats: Statistiques détaillées des désynchronisations
        test_games: Données des parties de test
        test_metrics: Métriques de performance du modèle
        sequence_patterns: Analyse des séquences prolongées (optionnel)

    Returns:
        Rapport d'analyse complet
    """
    # Calculer des métriques avancées
    sync_stats = detailed_stats['sync_states']

    # Différence d'impact entre SYNC et DESYNC
    sync_s_rate = sync_stats['SYNC']['S_rate']
    sync_o_rate = sync_stats['SYNC']['O_rate']
    desync_s_rate = sync_stats['DESYNC']['S_rate']
    desync_o_rate = sync_stats['DESYNC']['O_rate']

    # Calcul du biais de désynchronisation
    desync_bias = {
        'S_bias': desync_s_rate - sync_s_rate,
        'O_bias': desync_o_rate - sync_o_rate,
        'total_bias': abs(desync_s_rate - sync_s_rate) + abs(desync_o_rate - sync_o_rate)
    }

    # Analyse des séquences
    consecutive_sync = detailed_stats['sequence_analysis']['consecutive_sync']
    consecutive_desync = detailed_stats['sequence_analysis']['consecutive_desync']

    sequence_stats = {
        'avg_sync_length': sum(consecutive_sync) / len(consecutive_sync) if consecutive_sync else 0,
        'avg_desync_length': sum(consecutive_desync) / len(consecutive_desync) if consecutive_desync else 0,
        'max_sync_length': max(consecutive_sync) if consecutive_sync else 0,
        'max_desync_length': max(consecutive_desync) if consecutive_desync else 0
    }

    # Analyse par partie
    game_stats = detailed_stats['game_statistics']
    avg_desync_impact = sum(g['desync_impact'] for g in game_stats) / len(game_stats) if game_stats else 0

    # Recommandations d'ajustement des paramètres
    recommendations = generate_parameter_recommendations(detailed_stats, test_metrics)

    report = {
        'metadata': {
            'timestamp': datetime.now().isoformat(),
            'total_games_analyzed': len(test_games),
            'total_hands_analyzed': detailed_stats['total_hands'],
            'analysis_version': '1.0'
        },
        'model_performance': {
            'test_accuracy': test_metrics['test_accuracy'],
            'accuracy_std': test_metrics['accuracy_std'],
            'min_accuracy': test_metrics['min_game_accuracy'],
            'max_accuracy': test_metrics['max_game_accuracy']
        },
        'sync_desync_analysis': {
            'sync_state_distribution': {
                'SYNC_percentage': sync_stats['SYNC']['total'] / detailed_stats['total_hands'] * 100,
                'DESYNC_percentage': sync_stats['DESYNC']['total'] / detailed_stats['total_hands'] * 100
            },
            'conversion_rates': {
                'SYNC': {'S_rate': sync_s_rate, 'O_rate': sync_o_rate},
                'DESYNC': {'S_rate': desync_s_rate, 'O_rate': desync_o_rate}
            },
            'desync_bias': desync_bias,
            'transition_patterns': detailed_stats['transition_patterns']
        },
        'sequence_analysis': sequence_stats,
        'parity_impact': detailed_stats['parity_analysis'],
        'desync_triggers': detailed_stats['desync_triggers'],
        'game_level_stats': {
            'avg_desync_impact': avg_desync_impact,
            'games_with_high_desync': len([g for g in game_stats if g['desync_impact'] > 0.4]),
            'avg_s_conversions_per_game': sum(g['S_conversions'] for g in game_stats) / len(game_stats),
            'avg_o_conversions_per_game': sum(g['O_conversions'] for g in game_stats) / len(game_stats)
        },
        'parameter_recommendations': recommendations,
        'raw_statistics': detailed_stats
    }

    # Ajouter l'analyse des séquences prolongées si disponible
    if sequence_patterns:
        report['sequence_patterns_analysis'] = {
            'long_sequences_summary': {
                'sync_sequences_count': len(sequence_patterns['long_sync_sequences']),
                'desync_sequences_count': len(sequence_patterns['long_desync_sequences']),
                'longest_sync': sequence_patterns['long_sync_sequences'][0]['length'] if sequence_patterns['long_sync_sequences'] else 0,
                'longest_desync': sequence_patterns['long_desync_sequences'][0]['length'] if sequence_patterns['long_desync_sequences'] else 0
            },
            'prolonged_patterns': {
                'sync_prolonged_S_rate': sequence_patterns['sync_patterns']['S_rate'],
                'sync_prolonged_O_rate': sequence_patterns['sync_patterns']['O_rate'],
                'desync_prolonged_S_rate': sequence_patterns['desync_patterns']['S_rate'],
                'desync_prolonged_O_rate': sequence_patterns['desync_patterns']['O_rate']
            },
            'exploitable_patterns': sequence_patterns['exploitable_patterns'],
            'detailed_sequences': {
                'top_sync_sequences': sequence_patterns['long_sync_sequences'][:10],
                'top_desync_sequences': sequence_patterns['long_desync_sequences'][:10]
            }
        }

    return report

def generate_parameter_recommendations(detailed_stats: Dict, test_metrics: Dict) -> Dict:
    """
    Génère des recommandations d'ajustement des paramètres basées sur l'analyse

    Args:
        detailed_stats: Statistiques détaillées
        test_metrics: Métriques de performance

    Returns:
        Recommandations d'ajustement
    """
    recommendations = {
        'confidence_thresholds': {},
        'learning_rate_adjustments': {},
        'pattern_weights': {},
        'sync_desync_weights': {}
    }

    # Analyse du biais SYNC/DESYNC
    sync_stats = detailed_stats['sync_states']
    desync_s_rate = sync_stats['DESYNC']['S_rate']
    desync_o_rate = sync_stats['DESYNC']['O_rate']
    sync_s_rate = sync_stats['SYNC']['S_rate']
    sync_o_rate = sync_stats['SYNC']['O_rate']

    # Recommandations basées sur les biais observés
    if abs(desync_s_rate - 0.5) > 0.1:  # Biais significatif en DESYNC
        if desync_s_rate > 0.6:
            recommendations['sync_desync_weights']['desync_s_bonus'] = 0.2
            recommendations['confidence_thresholds']['desync_s_threshold'] = 0.3
        elif desync_s_rate < 0.4:
            recommendations['sync_desync_weights']['desync_o_bonus'] = 0.2
            recommendations['confidence_thresholds']['desync_o_threshold'] = 0.3

    # Ajustements basés sur la performance
    if test_metrics['test_accuracy'] < 0.55:
        recommendations['learning_rate_adjustments']['increase_learning_rate'] = True
        recommendations['confidence_thresholds']['lower_all_thresholds'] = 0.05
    elif test_metrics['test_accuracy'] > 0.65:
        recommendations['confidence_thresholds']['raise_all_thresholds'] = 0.05

    # Recommandations basées sur la variabilité
    if test_metrics['accuracy_std'] > 0.1:
        recommendations['pattern_weights']['increase_stability_weight'] = 0.1

    return recommendations

def display_key_statistics(analysis_report: Dict):
    """
    Affiche les statistiques clés du rapport d'analyse

    Args:
        analysis_report: Rapport d'analyse complet
    """
    print("\n📊 STATISTIQUES CLÉS DES DÉSYNCHRONISATIONS")
    print("=" * 50)

    # Performance du modèle
    perf = analysis_report['model_performance']
    print(f"\n🎯 Performance du modèle:")
    print(f"   Précision moyenne: {perf['test_accuracy']:.1%}")
    print(f"   Écart-type: {perf['accuracy_std']:.1%}")
    print(f"   Plage: {perf['min_accuracy']:.1%} - {perf['max_accuracy']:.1%}")

    # Distribution SYNC/DESYNC
    sync_analysis = analysis_report['sync_desync_analysis']
    dist = sync_analysis['sync_state_distribution']
    print(f"\n⚖️ Distribution des états:")
    print(f"   SYNC: {dist['SYNC_percentage']:.1f}%")
    print(f"   DESYNC: {dist['DESYNC_percentage']:.1f}%")

    # Taux de conversion
    conv = sync_analysis['conversion_rates']
    print(f"\n🔄 Taux de conversion S/O:")
    print(f"   SYNC → S: {conv['SYNC']['S_rate']:.1%}, O: {conv['SYNC']['O_rate']:.1%}")
    print(f"   DESYNC → S: {conv['DESYNC']['S_rate']:.1%}, O: {conv['DESYNC']['O_rate']:.1%}")

    # Biais de désynchronisation
    bias = sync_analysis['desync_bias']
    print(f"\n📈 Biais de désynchronisation:")
    print(f"   Biais S: {bias['S_bias']:+.1%}")
    print(f"   Biais O: {bias['O_bias']:+.1%}")
    print(f"   Biais total: {bias['total_bias']:.1%}")

    # Séquences
    seq = analysis_report['sequence_analysis']
    print(f"\n📏 Longueurs de séquences:")
    print(f"   SYNC moyen: {seq['avg_sync_length']:.1f} manches")
    print(f"   DESYNC moyen: {seq['avg_desync_length']:.1f} manches")
    print(f"   SYNC max: {seq['max_sync_length']} manches")
    print(f"   DESYNC max: {seq['max_desync_length']} manches")

    # Recommandations
    reco = analysis_report['parameter_recommendations']
    print(f"\n🎛️ Recommandations d'ajustement:")
    if reco['sync_desync_weights']:
        for key, value in reco['sync_desync_weights'].items():
            print(f"   {key}: {value:+.1f}")
    if reco['confidence_thresholds']:
        for key, value in reco['confidence_thresholds'].items():
            print(f"   {key}: {value:+.1f}")

    print(f"\n💡 Impact exploitable détecté: {bias['total_bias']:.1%}")
    if bias['total_bias'] > 0.1:
        print("   ✅ Biais significatif - Ajustement des paramètres recommandé")
    else:
        print("   ⚠️ Biais faible - Surveillance continue recommandée")

    # Afficher l'analyse des séquences prolongées si disponible
    if 'sequence_patterns_analysis' in analysis_report:
        seq_analysis = analysis_report['sequence_patterns_analysis']
        summary = seq_analysis['long_sequences_summary']
        patterns = seq_analysis['prolonged_patterns']
        exploitable = seq_analysis['exploitable_patterns']

        print(f"\n🔍 ANALYSE DES SÉQUENCES PROLONGÉES")
        print("=" * 40)

        print(f"\n📏 Séquences détectées (≥5 manches):")
        print(f"   SYNC prolongées: {summary['sync_sequences_count']}")
        print(f"   DESYNC prolongées: {summary['desync_sequences_count']}")
        print(f"   Plus longue SYNC: {summary['longest_sync']} manches")
        print(f"   Plus longue DESYNC: {summary['longest_desync']} manches")

        print(f"\n🎯 Patterns S/O dans séquences prolongées:")
        print(f"   SYNC prolongé → S: {patterns['sync_prolonged_S_rate']:.1%}, O: {patterns['sync_prolonged_O_rate']:.1%}")
        print(f"   DESYNC prolongé → S: {patterns['desync_prolonged_S_rate']:.1%}, O: {patterns['desync_prolonged_O_rate']:.1%}")

        # Calculer les biais des séquences prolongées
        sync_bias = abs(patterns['sync_prolonged_S_rate'] - 0.5)
        desync_bias = abs(patterns['desync_prolonged_S_rate'] - 0.5)

        print(f"\n⚡ Biais exploitables dans séquences prolongées:")
        if sync_bias > 0.1:
            direction = "S" if patterns['sync_prolonged_S_rate'] > 0.5 else "O"
            print(f"   🔥 SYNC prolongé → Biais {direction} de {sync_bias:.1%}")
        else:
            print(f"   ⚪ SYNC prolongé → Biais faible ({sync_bias:.1%})")

        if desync_bias > 0.1:
            direction = "S" if patterns['desync_prolonged_S_rate'] > 0.5 else "O"
            print(f"   🔥 DESYNC prolongé → Biais {direction} de {desync_bias:.1%}")
        else:
            print(f"   ⚪ DESYNC prolongé → Biais faible ({desync_bias:.1%})")

        # Afficher les recommandations spécifiques aux séquences
        if exploitable['recommendations']:
            print(f"\n🎛️ Recommandations séquences:")
            for rec in exploitable['recommendations']:
                print(f"   • {rec}")

        # Afficher les top séquences
        top_sync = seq_analysis['detailed_sequences']['top_sync_sequences'][:3]
        top_desync = seq_analysis['detailed_sequences']['top_desync_sequences'][:3]

        if top_sync:
            print(f"\n🏆 Top 3 séquences SYNC:")
            for i, seq in enumerate(top_sync, 1):
                print(f"   {i}. Longueur {seq['length']} → S:{seq['S_rate']:.1%} O:{seq['O_rate']:.1%}")

        if top_desync:
            print(f"\n🏆 Top 3 séquences DESYNC:")
            for i, seq in enumerate(top_desync, 1):
                print(f"   {i}. Longueur {seq['length']} → S:{seq['S_rate']:.1%} O:{seq['O_rate']:.1%}")

        # Conclusion sur l'exploitabilité
        max_bias = max(sync_bias, desync_bias)
        if max_bias > 0.15:
            print(f"\n🚀 POTENTIEL ÉLEVÉ: Biais de {max_bias:.1%} détecté dans séquences prolongées!")
        elif max_bias > 0.1:
            print(f"\n⚡ POTENTIEL MODÉRÉ: Biais de {max_bias:.1%} exploitable")
        else:
            print(f"\n📊 Séquences analysées - Biais faibles mais surveillance recommandée")

def create_azr_interface() -> AZRBaccaratInterface:
    """Crée une interface graphique AZR complète"""
    # Créer le prédicteur AZR
    predictor = create_azr_predictor()

    # Créer l'interface avec le prédicteur
    interface = AZRBaccaratInterface(predictor)
    interface.set_azr_predictor(predictor)

    return interface

def generate_games_batch(batch_info: Tuple[int, int, int]) -> List[Dict]:
    """
    Génère un lot de parties dans un processus séparé

    Args:
        batch_info: (start_game_number, num_games_in_batch, hands_per_game)

    Returns:
        Liste des parties générées
    """
    start_game, num_games, hands_per_game = batch_info

    # Créer un générateur local pour ce processus
    generator = BaccaratGenerator()
    games = []

    for i in range(num_games):
        game_number = start_game + i

        # Générer une partie
        game_data = generator.generate_game_data(hands_per_game)

        # Format compatible
        formatted_game = {
            'game_number': game_number,
            'initialization': {
                'burn_cards_count': game_data['burn_cards_count'],
                'burn_parity': game_data['burn_parity'],
                'initial_sync_state': 'SYNC' if game_data['burn_parity'] == 'PAIR' else 'DESYNC'
            },
            'statistics': {
                'total_hands': game_data['total_hands'],
                'pb_hands': game_data['pb_hands'],
                'tie_hands': game_data['tie_hands'],
                'so_conversions': len([h for h in game_data['hands'] if h.get('so_conversion', '--') != '--'])
            },
            'hands': game_data['hands']
        }

        games.append(formatted_game)

        # Libérer la mémoire périodiquement
        if i % 100 == 0:
            gc.collect()

    return games

def generate_and_analyze_statistical_only(num_games: int):
    """
    Génère les données et analyse les séquences SANS prédictions AZR (analyse statistique pure)

    Args:
        num_games: Nombre de parties à générer
    """
    try:
        print(f"\n🚀 Génération parallèle optimisée pour {num_games} parties")
        print(f"💻 Utilisation de {mp.cpu_count()} cœurs disponibles")
        print(f"💾 RAM disponible: {psutil.virtual_memory().available / (1024**3):.1f} GB")
        print("🎯 MODE: ANALYSE STATISTIQUE PURE (SANS prédictions AZR)")

        start_time = time.time()

        # Optimisation pour 8 cœurs et 28GB RAM
        max_workers = min(8, mp.cpu_count())

        # Calculer la taille des lots pour optimiser la mémoire
        batch_size = min(4000, max(500, num_games // max_workers))

        print(f"🔧 Configuration: {max_workers} processus, {batch_size} parties par lot")

        # Créer les lots de travail
        batches = []
        games_assigned = 0

        while games_assigned < num_games:
            remaining_games = num_games - games_assigned
            current_batch_size = min(batch_size, remaining_games)

            batches.append((games_assigned + 1, current_batch_size, 60))
            games_assigned += current_batch_size

        print(f"📦 {len(batches)} lots créés pour traitement parallèle")

        # Traitement parallèle
        all_games = []
        total_hands = 0
        total_pb_hands = 0
        total_so_conversions = 0

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Soumettre tous les lots
            future_to_batch = {executor.submit(generate_games_batch, batch): batch for batch in batches}

            # Collecter les résultats au fur et à mesure
            completed_batches = 0
            for future in as_completed(future_to_batch):
                batch_info = future_to_batch[future]

                try:
                    batch_games = future.result()
                    all_games.extend(batch_games)

                    # Calculer les statistiques
                    for game in batch_games:
                        total_hands += game['statistics']['total_hands']
                        total_pb_hands += game['statistics']['pb_hands']
                        total_so_conversions += game['statistics']['so_conversions']

                    completed_batches += 1
                    progress = (completed_batches / len(batches)) * 100
                    ram_usage = psutil.virtual_memory().percent

                    print(f"✅ Lot {completed_batches}/{len(batches)} terminé ({progress:.1f}%) - "
                          f"RAM: {ram_usage:.1f}% - Parties: {len(all_games)}")

                    # Libérer la mémoire
                    gc.collect()

                except Exception as e:
                    print(f"❌ Erreur dans le lot {batch_info}: {e}")

        generation_time = time.time() - start_time

        # Trier les parties par numéro
        all_games.sort(key=lambda x: x['game_number'])

        print(f"\n⏱️ Génération terminée en {generation_time:.1f} secondes")
        print(f"📊 {len(all_games)} parties générées avec {total_pb_hands:,} manches P/B")

        # Sauvegarder les données
        filename = f"azr_statistical_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        training_data = {
            'metadata': {
                'total_games': len(all_games),
                'total_hands': total_hands,
                'total_pb_hands': total_pb_hands,
                'total_so_conversions': total_so_conversions,
                'hands_per_game': 60,
                'generation_time': generation_time,
                'parallel_workers': max_workers,
                'batch_size': batch_size,
                'analysis_mode': 'STATISTICAL_ONLY',
                'timestamp': datetime.now().isoformat()
            },
            'games': all_games
        }

        print(f"\n💾 Sauvegarde des données...")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, indent=2, ensure_ascii=False)

        print(f"✅ Données sauvegardées: {filename}")

        # ANALYSE STATISTIQUE PURE (SANS PRÉDICTIONS)
        print(f"\n📊 ANALYSE STATISTIQUE PURE DES SÉQUENCES SYNC/DESYNC...")
        analysis_start = time.time()

        # Analyser l'impact des désynchronisations
        detailed_stats = analyze_desync_impact(all_games)

        # Analyser les séquences prolongées SYNC/DESYNC
        sequence_patterns = analyze_sequence_patterns(all_games)

        analysis_time = time.time() - analysis_start

        print(f"\n⏱️ Analyse statistique terminée en {analysis_time:.1f} secondes")

        # Sauvegarder l'analyse statistique
        analysis_filename = f"azr_statistical_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        save_statistical_analysis_only(detailed_stats, sequence_patterns, analysis_filename,
                                     generation_time, analysis_time, len(all_games), total_pb_hands)

        print(f"\n💾 Analyse statistique sauvegardée: {analysis_filename}")

        # Afficher les résultats clés
        display_statistical_summary(detailed_stats, sequence_patterns)

        print(f"\n🎉 ANALYSE STATISTIQUE TERMINÉE AVEC SUCCÈS !")
        print(f"📊 Temps total: {(time.time() - start_time):.1f} secondes")
        print(f"⚡ Vitesse: {len(all_games)/(time.time() - start_time):.1f} parties/seconde")

    except Exception as e:
        print(f"❌ Erreur lors de l'analyse statistique: {e}")
        import traceback
        traceback.print_exc()

def generate_and_analyze_sequences(predictor: AZRBaccaratPredictor, num_games: int):
    """
    Génère les données et analyse les séquences avec optimisation multi-cœurs

    Args:
        predictor: Instance du prédicteur AZR
        num_games: Nombre de parties à générer
    """
    try:
        print(f"\n🚀 Génération parallèle optimisée pour {num_games} parties")
        print(f"💻 Utilisation de {mp.cpu_count()} cœurs disponibles")
        print(f"💾 RAM disponible: {psutil.virtual_memory().available / (1024**3):.1f} GB")

        start_time = time.time()

        # Optimisation pour 8 cœurs et 28GB RAM
        max_workers = min(8, mp.cpu_count())

        # Calculer la taille des lots pour optimiser la mémoire
        # Avec 28GB RAM, on peut traiter ~4000 parties par lot
        batch_size = min(4000, max(500, num_games // max_workers))

        print(f"🔧 Configuration: {max_workers} processus, {batch_size} parties par lot")

        # Créer les lots de travail
        batches = []
        games_assigned = 0

        while games_assigned < num_games:
            remaining_games = num_games - games_assigned
            current_batch_size = min(batch_size, remaining_games)

            batches.append((games_assigned + 1, current_batch_size, 60))
            games_assigned += current_batch_size

        print(f"📦 {len(batches)} lots créés pour traitement parallèle")

        # Traitement parallèle
        all_games = []
        total_hands = 0
        total_pb_hands = 0
        total_so_conversions = 0

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Soumettre tous les lots
            future_to_batch = {executor.submit(generate_games_batch, batch): batch for batch in batches}

            # Collecter les résultats au fur et à mesure
            completed_batches = 0
            for future in as_completed(future_to_batch):
                batch_info = future_to_batch[future]

                try:
                    batch_games = future.result()
                    all_games.extend(batch_games)

                    # Calculer les statistiques
                    for game in batch_games:
                        total_hands += game['statistics']['total_hands']
                        total_pb_hands += game['statistics']['pb_hands']
                        total_so_conversions += game['statistics']['so_conversions']

                    completed_batches += 1
                    progress = (completed_batches / len(batches)) * 100
                    ram_usage = psutil.virtual_memory().percent

                    print(f"✅ Lot {completed_batches}/{len(batches)} terminé ({progress:.1f}%) - "
                          f"RAM: {ram_usage:.1f}% - Parties: {len(all_games)}")

                    # Libérer la mémoire
                    gc.collect()

                except Exception as e:
                    print(f"❌ Erreur dans le lot {batch_info}: {e}")

        generation_time = time.time() - start_time

        # Trier les parties par numéro
        all_games.sort(key=lambda x: x['game_number'])

        print(f"\n⏱️ Génération terminée en {generation_time:.1f} secondes")
        print(f"📊 {len(all_games)} parties générées avec {total_pb_hands} manches P/B")

        # Sauvegarder les données
        filename = f"azr_generated_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        training_data = {
            'metadata': {
                'total_games': len(all_games),
                'total_hands': total_hands,
                'total_pb_hands': total_pb_hands,
                'total_so_conversions': total_so_conversions,
                'hands_per_game': 60,
                'generation_time': generation_time,
                'parallel_workers': max_workers,
                'batch_size': batch_size,
                'timestamp': datetime.now().isoformat()
            },
            'games': all_games
        }

        print(f"\n💾 Sauvegarde des données...")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, indent=2, ensure_ascii=False)

        print(f"✅ Données sauvegardées: {filename}")

        # Analyser les séquences sur un échantillon
        print(f"\n🔍 Analyse des séquences maximales...")
        analysis_start = time.time()

        # Analyser toutes les parties pour les séquences maximales
        print(f"📊 Analyse complète sur {len(all_games)} parties...")

        # Réinitialiser le prédicteur pour l'analyse
        predictor.reset_session()

        # Traiter les parties une par une pour l'analyse des intervalles
        games_processed = 0
        for game in all_games:
            # Réinitialiser pour chaque partie
            predictor.reset_session()

            # Initialiser le brûlage
            burn_parity = game['initialization']['burn_parity']
            predictor.set_burn_parity(burn_parity)

            # Traiter toutes les manches de la partie
            for hand_data in game['hands']:
                predictor.receive_hand_data(hand_data)

            games_processed += 1
            if games_processed % 10000 == 0:
                print(f"   Parties analysées: {games_processed}/{len(all_games)} ({games_processed/len(all_games)*100:.1f}%)")
                gc.collect()  # Libérer la mémoire

        # Analyser les séquences maximales
        analysis = predictor.analyze_maximum_sequences()

        analysis_time = time.time() - analysis_start
        print(f"\n⏱️ Analyse terminée en {analysis_time:.1f} secondes")

        # Afficher le rapport complet
        print(f"\n" + "="*80)
        print(f"🎯 RAPPORT D'ANALYSE DES SÉQUENCES ({len(all_games)} parties)")
        print(f"="*80)

        predictor.print_maximum_sequences_report(analysis)

        # Sauvegarder le rapport en format texte pour lecture détaillée
        report_filename = f"rapport_sequences_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        # Capturer le rapport dans un fichier texte
        import io
        import sys

        # Rediriger la sortie vers un buffer
        old_stdout = sys.stdout
        sys.stdout = buffer = io.StringIO()

        # Générer le rapport complet dans le buffer
        print(f"RAPPORT D'ANALYSE DES SÉQUENCES SYNC/DESYNC")
        print(f"="*80)
        print(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Parties analysées: {len(all_games):,}")
        print(f"Temps de génération: {generation_time:.1f} secondes")
        print(f"Temps d'analyse: {analysis_time:.1f} secondes")
        print(f"Processus parallèles: {max_workers}")
        print(f"="*80)

        # Afficher le rapport détaillé
        predictor.print_maximum_sequences_report(analysis)

        # Ajouter des statistiques supplémentaires
        print(f"\n" + "="*80)
        print(f"STATISTIQUES TECHNIQUES")
        print(f"="*80)
        print(f"Total manches générées: {total_hands:,}")
        print(f"Total manches P/B: {total_pb_hands:,}")
        print(f"Total conversions S/O: {total_so_conversions:,}")
        print(f"Ratio P/B: {total_pb_hands/total_hands:.3f}")
        print(f"Ratio conversions: {total_so_conversions/total_pb_hands:.3f}")

        # Récupérer le contenu du buffer
        report_content = buffer.getvalue()

        # Restaurer stdout
        sys.stdout = old_stdout

        # Sauvegarder le rapport texte
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"\n📄 Rapport texte sauvegardé: {report_filename}")

        # Sauvegarder les résultats d'analyse JSON
        analysis_filename = f"sequence_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(analysis_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_games_generated': len(all_games),
                    'games_analyzed': len(all_games),
                    'generation_time': generation_time,
                    'analysis_time': analysis_time,
                    'total_time': time.time() - start_time,
                    'parallel_workers': max_workers,
                    'timestamp': datetime.now().isoformat()
                },
                'analysis_results': analysis
            }, f, indent=2, ensure_ascii=False, default=str)

        print(f"💾 Données JSON sauvegardées: {analysis_filename}")

        total_time = time.time() - start_time
        print(f"\n🎉 PROCESSUS COMPLET TERMINÉ en {total_time:.1f} secondes")
        print(f"📈 Prêt pour l'exploitation des biais détectés !")

    except Exception as e:
        print(f"\n❌ Erreur lors de la génération/analyse: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Fonction principale du programme"""
    # Choix du mode de démonstration
    print("🎯 MODÈLE AZR BACCARAT INTÉGRÉ")
    print("=" * 40)
    print("1. Interface graphique AZR (recommandé)")
    print("2. Démonstration complète (entraînement + test)")
    print("3. Test simple")
    print("4. Génération de données seulement")
    print("5. 📊 Analyse statistique pure SANS prédictions")
    print("6. 📝 Génération formatée avec index cohérents")
    print("7. 🏁 Benchmark optimisations CPU (8 cœurs + 28GB RAM)")

    choice = input("\nVotre choix (1/2/3/4/5/6/7): ").strip()

    if choice == "1":
        # Interface graphique AZR
        print("\n🎮 Lancement de l'interface graphique AZR")
        print("Interface avec prédictions en temps réel, entraînement intégré")
        print("et toutes les fonctionnalités AZR disponibles.")

        try:
            interface = create_azr_interface()
            interface.run()
        except Exception as e:
            print(f"❌ Erreur lors du lancement de l'interface: {e}")
            print("Vérifiez que tkinter est installé et disponible.")

    elif choice == "2":
        demo_training_and_testing_with_analysis()

    elif choice == "3":
        # Test simple
        print("\n🧪 Test simple du modèle AZR")
        predictor = create_azr_predictor()

        # Simuler quelques manches
        test_hands = [
            {'pb_hand_number': 1, 'result': 'PLAYER', 'parity': 'PAIR', 'sync_state': 'SYNC', 'so_conversion': '--'},
            {'pb_hand_number': 2, 'result': 'BANKER', 'parity': 'IMPAIR', 'sync_state': 'DESYNC', 'so_conversion': 'O'},
            {'pb_hand_number': 3, 'result': 'PLAYER', 'parity': 'PAIR', 'sync_state': 'SYNC', 'so_conversion': 'O'},
            {'pb_hand_number': 4, 'result': 'PLAYER', 'parity': 'IMPAIR', 'sync_state': 'DESYNC', 'so_conversion': 'S'}
        ]

        print("\n📊 Simulation de manches:")
        for hand in test_hands:
            prediction = predictor.receive_hand_data(hand)
            print(f"Manche {hand['pb_hand_number']}: {hand['result']} {hand['parity']} → Prédiction: {prediction}")

        # Afficher les statistiques
        stats = predictor.get_statistics()
        print(f"\n📈 Statistiques finales:")
        print(f"Précision: {stats['performance']['current_accuracy']:.3f}")
        print(f"Total prédictions: {stats['performance']['total_predictions']}")
        print("\n✅ Test terminé")

    elif choice == "4":
        # GÉNÉRATION MASSIVE POUR ANALYSE STATISTIQUE
        print("\n🎲 GÉNÉRATION MASSIVE POUR ANALYSE STATISTIQUE")
        print("=" * 60)

        predictor = create_azr_predictor()

        # Options de génération
        print("Options disponibles:")
        print("1. Génération standard (100 000 parties)")
        print("2. Génération personnalisée")

        gen_choice = input("Votre choix (1-2): ").strip()

        if gen_choice == "1":
            num_games = 100000
            hands_per_game = 60
        else:
            num_games = int(input("Nombre de parties à générer: ") or "100000")
            hands_per_game = int(input("Manches P/B par partie (défaut 60): ") or "60")

        print(f"\n🚀 Génération de {num_games:,} parties en cours...")
        print("⚠️  Cette opération peut prendre plusieurs minutes...")

        # Génération avec optimisation mémoire
        start_time = time.time()
        training_data = predictor.generate_massive_data_optimized(num_games, hands_per_game)
        end_time = time.time()

        # Sauvegarde avec nom spécifique pour analyse
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"baccarat_analysis_data_{num_games}games_{timestamp}.json"

        print(f"💾 Sauvegarde en cours...")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, indent=2, ensure_ascii=False)

        # Rapport final
        duration = end_time - start_time
        total_hands = sum(game['statistics']['total_hands'] for game in training_data['games'])

        print(f"\n✅ GÉNÉRATION TERMINÉE")
        print(f"📁 Fichier créé: {filename}")
        print(f"🎯 Parties générées: {num_games:,}")
        print(f"🎲 Total manches: {total_hands:,}")
        print(f"⏱️  Durée: {duration:.1f} secondes")
        print(f"⚡ Vitesse: {num_games/duration:.0f} parties/seconde")
        print(f"\n🔍 Utilisez 'python baccarat_data_analyzer.py {filename}' pour analyser les données")

    elif choice == "5":
        # NOUVELLE OPTION: Analyse statistique pure SANS prédictions
        print("\n📊 Analyse statistique pure SANS prédictions AZR")
        print("🎯 Mode: Génération + Analyse statistique des séquences SYNC/DESYNC")

        num_games = int(input("Nombre de parties à générer (défaut 100000): ") or "100000")

        # Lancer l'analyse statistique pure
        print(f"\n🚀 Lancement de l'analyse statistique pure sur {num_games} parties...")
        print("⏳ Génération et analyse en cours...")

        generation_thread = threading.Thread(
            target=generate_and_analyze_statistical_only,
            args=(num_games,),
            daemon=True
        )
        generation_thread.start()

        # Attendre la fin du thread avec affichage du progrès
        while generation_thread.is_alive():
            print(".", end="", flush=True)
            time.sleep(2)

        print("\n✅ Analyse statistique pure terminée!")

    elif choice == "6":
        # NOUVELLE OPTION: Génération formatée avec index cohérents
        print("\n📝 GÉNÉRATION FORMATÉE AVEC INDEX COHÉRENTS")
        print("=" * 60)
        print("🎯 Format de sortie avec toutes les séquences alignées:")
        print("   - INDEX, PAIR/IMPAIR, SYNC/DESYNC, COMBINÉ, S/O, MANCHES")
        print("   - Index numérotés et cohérents avec statistiques détaillées")

        predictor = create_azr_predictor()

        # Options de génération
        print("\nOptions disponibles:")
        print("1. Génération rapide (100 parties complètes)")
        print("2. Génération standard (1 000 parties complètes)")
        print("3. Génération personnalisée")

        gen_choice = input("Votre choix (1-3): ").strip()

        if gen_choice == "1":
            num_games = 100
            hands_per_game = None  # Parties complètes jusqu'au cut card
        elif gen_choice == "2":
            num_games = 1000
            hands_per_game = None  # Parties complètes jusqu'au cut card
        else:
            num_games = int(input("Nombre de parties à générer: ") or "100")
            limit_choice = input("Limiter les manches P/B ? (o/N): ").strip().lower()
            if limit_choice in ['o', 'oui', 'y', 'yes']:
                hands_per_game = int(input("Limite manches P/B: ") or "60")
            else:
                hands_per_game = None  # Parties complètes

        # Nom du fichier personnalisé
        custom_filename = input("Nom du fichier (optionnel): ").strip()
        filename = custom_filename if custom_filename else None

        print(f"\n🚀 Génération de {num_games:,} parties formatées...")
        print("⏳ Génération et formatage en cours...")

        start_time = time.time()
        created_filename = predictor.generate_and_save_formatted_games(
            num_games=num_games,
            hands_per_game=hands_per_game,
            filename=filename
        )
        end_time = time.time()

        duration = end_time - start_time

        print(f"\n✅ GÉNÉRATION FORMATÉE TERMINÉE")
        print(f"📁 Fichier créé: {created_filename}")
        print(f"🎯 Parties générées: {num_games:,}")
        print(f"⏱️  Durée: {duration:.1f} secondes")
        print(f"⚡ Vitesse: {num_games/duration:.0f} parties/seconde")
        if hands_per_game is None:
            print(f"🎲 Mode: Parties complètes jusqu'au cut card (75%)")
        else:
            print(f"🎲 Mode: Limité à {hands_per_game} manches P/B")
        print(f"\n📖 Le fichier contient toutes les séquences avec index numérotés")
        print(f"🔍 Format: INDEX, PAIR/IMPAIR, SYNC/DESYNC, COMBINÉ, S/O, MANCHES alignés")

    elif choice == "7":
        # NOUVEAU: Benchmark des optimisations CPU
        print("\n🏁 BENCHMARK DES OPTIMISATIONS CPU")
        print("=" * 60)
        print("🎯 Test des performances avec optimisations 8 cœurs + 28GB RAM")
        print("⚡ Comparaison rollouts parallèles vs séquentiels")
        print("💾 Test génération massive avec monitoring mémoire")
        print("📊 Évaluation performance globale du système")

        predictor = create_azr_predictor()

        # Options de benchmark
        print("\nOptions de benchmark:")
        print("1. Benchmark rapide (100 rollouts, 1K parties)")
        print("2. Benchmark standard (500 rollouts, 5K parties)")
        print("3. Benchmark intensif (1000 rollouts, 10K parties)")
        print("4. Benchmark personnalisé")

        bench_choice = input("Votre choix (1-4): ").strip()

        if bench_choice == "1":
            test_rollouts = 100
            test_generation = 1000
        elif bench_choice == "2":
            test_rollouts = 500
            test_generation = 5000
        elif bench_choice == "3":
            test_rollouts = 1000
            test_generation = 10000
        else:
            test_rollouts = int(input("Nombre de rollouts à tester: ") or "500")
            test_generation = int(input("Nombre de parties à générer: ") or "5000")

        print(f"\n🚀 Lancement du benchmark...")
        print(f"   Rollouts à tester: {test_rollouts}")
        print(f"   Parties à générer: {test_generation}")
        print("⏳ Benchmark en cours...")

        # Afficher les stats hardware avant
        hw_stats = predictor.get_hardware_performance_stats()
        print(f"\n💻 Configuration hardware:")
        print(f"   CPU: {hw_stats['hardware_config']['cpu_cores']} cœurs")
        print(f"   RAM: {hw_stats['memory_stats']['total_gb']:.1f} GB total")
        print(f"   RAM disponible: {hw_stats['memory_stats']['available_gb']:.1f} GB")

        # Lancer le benchmark
        start_time = time.time()
        benchmark_results = predictor.benchmark_cpu_performance(test_rollouts, test_generation)
        total_time = time.time() - start_time

        # Afficher les résultats détaillés
        print(f"\n🏁 RÉSULTATS DU BENCHMARK")
        print("=" * 50)

        rollouts_test = benchmark_results['rollouts_test']
        print(f"\n🔄 Test Rollouts:")
        print(f"   Séquentiel: {rollouts_test['sequential_time']:.3f}s")
        print(f"   Parallèle: {rollouts_test['parallel_time']:.3f}s")
        print(f"   Speedup: {rollouts_test['speedup']:.2f}x")
        print(f"   Efficacité: {rollouts_test['efficiency_percent']:.1f}%")

        generation_test = benchmark_results['generation_test']
        print(f"\n🎲 Test Génération:")
        print(f"   Temps: {generation_test['generation_time']:.1f}s")
        print(f"   Parties/seconde: {generation_test['games_per_second']:.1f}")
        print(f"   Mémoire/partie: {generation_test['memory_per_game_mb']:.2f} MB")
        print(f"   Augmentation mémoire: {generation_test['memory_increase_gb']:.2f} GB")

        system_test = benchmark_results['system_test']
        print(f"\n⚡ Test Système:")
        print(f"   Prédictions/seconde: {system_test['predictions_per_second']:.1f}")
        print(f"   CPU avant: {system_test['cpu_before_percent']:.1f}%")
        print(f"   CPU après: {system_test['cpu_after_percent']:.1f}%")

        summary = benchmark_results['summary']
        print(f"\n📊 Résumé:")
        print(f"   Temps total benchmark: {total_time:.1f}s")
        print(f"   Performance globale: {summary['overall_performance']}")
        print(f"   Efficacité parallèle: {summary['parallel_efficiency']:.1f}%")

        # Recommandations
        hw_stats_after = predictor.get_hardware_performance_stats()
        recommendations = hw_stats_after['recommendations']
        print(f"\n💡 Recommandations:")
        for rec in recommendations:
            print(f"   {rec}")

        # Sauvegarde des résultats
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        benchmark_filename = f"benchmark_cpu_results_{timestamp}.json"

        benchmark_data = {
            'timestamp': datetime.now().isoformat(),
            'hardware_config': hw_stats['hardware_config'],
            'test_parameters': {
                'test_rollouts': test_rollouts,
                'test_generation': test_generation
            },
            'results': benchmark_results,
            'recommendations': recommendations
        }

        with open(benchmark_filename, 'w', encoding='utf-8') as f:
            json.dump(benchmark_data, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Résultats sauvegardés: {benchmark_filename}")
        print(f"✅ Benchmark terminé avec succès!")

    else:
        print("❌ Choix invalide")

def save_statistical_analysis_only(detailed_stats: Dict, sequence_patterns: Dict, filename: str,
                                 generation_time: float, analysis_time: float, total_games: int, total_pb_hands: int):
    """
    Sauvegarde l'analyse statistique pure dans un fichier texte

    Args:
        detailed_stats: Statistiques détaillées des désynchronisations
        sequence_patterns: Analyse des séquences prolongées
        filename: Nom du fichier de sauvegarde
        generation_time: Temps de génération
        analysis_time: Temps d'analyse
        total_games: Nombre total de parties
        total_pb_hands: Nombre total de manches P/B
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("="*80 + "\n")
            f.write("RAPPORT D'ANALYSE STATISTIQUE PURE DES SÉQUENCES SYNC/DESYNC\n")
            f.write("="*80 + "\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Mode: ANALYSE STATISTIQUE PURE (SANS prédictions AZR)\n")
            f.write(f"Parties analysées: {total_games:,}\n")
            f.write(f"Manches P/B totales: {total_pb_hands:,}\n")
            f.write(f"Temps de génération: {generation_time:.1f} secondes\n")
            f.write(f"Temps d'analyse: {analysis_time:.1f} secondes\n")
            f.write(f"Vitesse: {total_games/generation_time:.1f} parties/seconde\n")
            f.write("="*80 + "\n\n")

            # Statistiques SYNC/DESYNC
            sync_stats = detailed_stats['sync_states']
            f.write("📊 DISTRIBUTION DES ÉTATS SYNC/DESYNC\n")
            f.write("-"*50 + "\n")
            f.write(f"SYNC: {sync_stats['SYNC']['total']:,} manches ({sync_stats['SYNC']['total']/detailed_stats['total_hands']*100:.1f}%)\n")
            f.write(f"DESYNC: {sync_stats['DESYNC']['total']:,} manches ({sync_stats['DESYNC']['total']/detailed_stats['total_hands']*100:.1f}%)\n\n")

            # Taux de conversion S/O
            f.write("🔄 TAUX DE CONVERSION S/O\n")
            f.write("-"*50 + "\n")
            f.write(f"SYNC → S: {sync_stats['SYNC']['S_rate']:.1%}, O: {sync_stats['SYNC']['O_rate']:.1%}\n")
            f.write(f"DESYNC → S: {sync_stats['DESYNC']['S_rate']:.1%}, O: {sync_stats['DESYNC']['O_rate']:.1%}\n\n")

            # Biais de désynchronisation
            desync_s_rate = sync_stats['DESYNC']['S_rate']
            sync_s_rate = sync_stats['SYNC']['S_rate']
            s_bias = desync_s_rate - sync_s_rate
            o_bias = (1 - desync_s_rate) - (1 - sync_s_rate)
            total_bias = abs(s_bias) + abs(o_bias)

            f.write("📈 BIAIS DE DÉSYNCHRONISATION\n")
            f.write("-"*50 + "\n")
            f.write(f"Biais S: {s_bias:+.1%}\n")
            f.write(f"Biais O: {o_bias:+.1%}\n")
            f.write(f"Biais total: {total_bias:.1%}\n\n")

            # Analyse des séquences
            if sequence_patterns:
                f.write("🔍 ANALYSE DES SÉQUENCES PROLONGÉES (≥5 manches)\n")
                f.write("-"*50 + "\n")

                summary = sequence_patterns.get('long_sequences_summary', {})
                f.write(f"Séquences SYNC prolongées: {summary.get('sync_sequences_count', 0)}\n")
                f.write(f"Séquences DESYNC prolongées: {summary.get('desync_sequences_count', 0)}\n")
                f.write(f"Plus longue SYNC: {summary.get('longest_sync', 0)} manches\n")
                f.write(f"Plus longue DESYNC: {summary.get('longest_desync', 0)} manches\n\n")

                patterns = sequence_patterns.get('prolonged_patterns', {})
                f.write("🎯 PATTERNS S/O DANS SÉQUENCES PROLONGÉES\n")
                f.write("-"*30 + "\n")
                f.write(f"SYNC prolongé → S: {patterns.get('sync_prolonged_S_rate', 0):.1%}, O: {patterns.get('sync_prolonged_O_rate', 0):.1%}\n")
                f.write(f"DESYNC prolongé → S: {patterns.get('desync_prolonged_S_rate', 0):.1%}, O: {patterns.get('desync_prolonged_O_rate', 0):.1%}\n\n")

                # Top séquences
                top_sync = sequence_patterns.get('detailed_sequences', {}).get('top_sync_sequences', [])[:10]
                top_desync = sequence_patterns.get('detailed_sequences', {}).get('top_desync_sequences', [])[:10]

                if top_sync:
                    f.write("🏆 TOP 10 SÉQUENCES SYNC\n")
                    f.write("-"*30 + "\n")
                    for i, seq in enumerate(top_sync, 1):
                        f.write(f"{i:2d}. Longueur {seq['length']:2d} → S:{seq['S_rate']:.1%} O:{seq['O_rate']:.1%} (Partie {seq.get('game_idx', '?')})\n")
                    f.write("\n")

                if top_desync:
                    f.write("🏆 TOP 10 SÉQUENCES DESYNC\n")
                    f.write("-"*30 + "\n")
                    for i, seq in enumerate(top_desync, 1):
                        f.write(f"{i:2d}. Longueur {seq['length']:2d} → S:{seq['S_rate']:.1%} O:{seq['O_rate']:.1%} (Partie {seq.get('game_idx', '?')})\n")
                    f.write("\n")

            # Conclusion
            f.write("💡 CONCLUSION\n")
            f.write("-"*50 + "\n")
            if total_bias > 0.15:
                f.write(f"🚀 POTENTIEL ÉLEVÉ: Biais de {total_bias:.1%} détecté!\n")
                f.write("   Recommandation: Ajustement immédiat des paramètres AZR\n")
            elif total_bias > 0.1:
                f.write(f"⚡ POTENTIEL MODÉRÉ: Biais de {total_bias:.1%} exploitable\n")
                f.write("   Recommandation: Test avec paramètres ajustés\n")
            else:
                f.write(f"📊 Biais faibles ({total_bias:.1%}) - Surveillance continue recommandée\n")

            f.write("\n" + "="*80 + "\n")
            f.write("FIN DU RAPPORT D'ANALYSE STATISTIQUE\n")
            f.write("="*80 + "\n")

        print(f"✅ Rapport statistique sauvegardé: {filename}")

    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde: {e}")

def display_statistical_summary(detailed_stats: Dict, sequence_patterns: Dict):
    """
    Affiche un résumé des statistiques clés

    Args:
        detailed_stats: Statistiques détaillées
        sequence_patterns: Analyse des séquences
    """
    print(f"\n📊 RÉSUMÉ STATISTIQUE CLÉS")
    print("="*50)

    # Statistiques SYNC/DESYNC
    sync_stats = detailed_stats['sync_states']
    desync_s_rate = sync_stats['DESYNC']['S_rate']
    sync_s_rate = sync_stats['SYNC']['S_rate']
    s_bias = desync_s_rate - sync_s_rate
    total_bias = abs(s_bias) + abs((1 - desync_s_rate) - (1 - sync_s_rate))

    print(f"\n⚖️ Distribution:")
    print(f"   SYNC: {sync_stats['SYNC']['total']/detailed_stats['total_hands']*100:.1f}%")
    print(f"   DESYNC: {sync_stats['DESYNC']['total']/detailed_stats['total_hands']*100:.1f}%")

    print(f"\n🔄 Conversions S/O:")
    print(f"   SYNC → S:{sync_stats['SYNC']['S_rate']:.1%} O:{sync_stats['SYNC']['O_rate']:.1%}")
    print(f"   DESYNC → S:{sync_stats['DESYNC']['S_rate']:.1%} O:{sync_stats['DESYNC']['O_rate']:.1%}")

    print(f"\n📈 Biais détecté:")
    print(f"   Biais S: {s_bias:+.1%}")
    print(f"   Biais total: {total_bias:.1%}")

    if sequence_patterns:
        summary = sequence_patterns.get('long_sequences_summary', {})
        print(f"\n🔍 Séquences prolongées:")
        print(f"   SYNC: {summary.get('sync_sequences_count', 0)} séquences")
        print(f"   DESYNC: {summary.get('desync_sequences_count', 0)} séquences")
        print(f"   Max SYNC: {summary.get('longest_sync', 0)} manches")
        print(f"   Max DESYNC: {summary.get('longest_desync', 0)} manches")

    print(f"\n💡 Évaluation:")
    if total_bias > 0.15:
        print("   🚀 POTENTIEL ÉLEVÉ - Biais exploitable détecté!")
    elif total_bias > 0.1:
        print("   ⚡ POTENTIEL MODÉRÉ - Ajustements recommandés")
    else:
        print("   📊 Biais faibles - Surveillance continue")

if __name__ == "__main__":
    main()
