# 🏗️ MODULE 2.1 - ARCHITECTURE GÉNÉRALE

## 🎯 **OBJECTIFS DU MODULE**

À la fin de ce module, vous comprendrez :
- ✅ L'architecture complète d'un système AZR
- ✅ Les composants principaux et leurs interactions
- ✅ Le flux de données et de contrôle
- ✅ Les patterns d'implémentation recommandés

---

## 🏛️ **VUE D'ENSEMBLE ARCHITECTURALE**

### 🎨 **Schéma Architectural Principal**

```mermaid
graph TB
    subgraph "🧠 MODÈLE UNIFIÉ"
        LLM[Large Language Model θ]
    end
    
    subgraph "🎭 RÔLES DUAUX"
        PROP[🎯 Proposeur π^propose]
        SOLV[🔧 Résolveur π^solve]
    end
    
    subgraph "🌍 ENVIRONNEMENT"
        ENV[🐍 Python Executor]
        VAL[✅ Validator]
        MET[📊 Metrics]
    end
    
    subgraph "💾 MÉMOIRE"
        BUFF[📚 Task Buffer]
        HIST[📈 History]
    end
    
    subgraph "⚙️ CONTRÔLE"
        CTRL[🎛️ Controller]
        OPT[🔄 Optimizer]
    end
    
    LLM --> PROP
    LLM --> SOLV
    PROP --> ENV
    SOLV --> ENV
    ENV --> VAL
    VAL --> MET
    MET --> CTRL
    CTRL --> OPT
    OPT --> LLM
    ENV --> BUFF
    BUFF --> PROP
    HIST --> CTRL
```

### 🔍 **Composants Principaux**

#### **1. 🧠 Modèle Unifié (Core)**
- **Rôle** : Cerveau central du système
- **Type** : Large Language Model (7B+ paramètres)
- **Fonction** : Génération et compréhension de texte/code

#### **2. 🎭 Rôles Duaux (Dual Roles)**
- **Proposeur** : Crée de nouvelles tâches d'apprentissage
- **Résolveur** : Résout les tâches proposées
- **Innovation** : Même modèle, prompts différents

#### **3. 🌍 Environnement (Environment)**
- **Executor** : Exécute le code de manière sécurisée
- **Validator** : Vérifie la correctness des solutions
- **Metrics** : Calcule les métriques de qualité

#### **4. 💾 Système de Mémoire (Memory)**
- **Task Buffer** : Stocke les tâches passées
- **History** : Historique des performances
- **Context** : Contexte pour la génération

#### **5. ⚙️ Système de Contrôle (Control)**
- **Controller** : Orchestre le processus d'entraînement
- **Optimizer** : Met à jour les paramètres du modèle

---

## 🧠 **MODÈLE UNIFIÉ - CŒUR DU SYSTÈME**

### 🎯 **Architecture du LLM**

#### **Spécifications Recommandées**
```yaml
Architecture: Transformer (GPT-style ou Llama-style)
Paramètres: 7B+ (minimum), 13B+ (optimal)
Context Length: 4096+ tokens
Vocabulary: 50K+ tokens incluant code
Training: Pré-entraîné sur code + texte
```

#### **Adaptations Spécifiques AZR**
```python
class AZRModel(nn.Module):
    def __init__(self, base_model):
        super().__init__()
        self.base_model = base_model  # LLM pré-entraîné
        
        # Têtes spécialisées pour AZR
        self.propose_head = nn.Linear(hidden_size, vocab_size)
        self.solve_head = nn.Linear(hidden_size, vocab_size)
        
        # Embeddings de rôle
        self.role_embeddings = nn.Embedding(2, hidden_size)  # propose/solve
        
    def forward(self, input_ids, role_id, **kwargs):
        # Injection du rôle dans les embeddings
        role_embed = self.role_embeddings(role_id)
        
        # Forward pass avec contexte de rôle
        outputs = self.base_model(input_ids, **kwargs)
        hidden_states = outputs.last_hidden_state + role_embed
        
        # Sélection de la tête selon le rôle
        if role_id == 0:  # Proposeur
            logits = self.propose_head(hidden_states)
        else:  # Résolveur
            logits = self.solve_head(hidden_states)
            
        return logits
```

### 🎭 **Mécanisme de Rôles Duaux**

#### **Commutation de Rôles**
```python
def switch_role(self, role: str):
    """Commute entre les rôles proposeur et résolveur"""
    if role == "propose":
        self.current_role = 0
        self.system_prompt = PROPOSE_SYSTEM_PROMPT
        self.temperature = 0.8  # Plus créatif
    elif role == "solve":
        self.current_role = 1
        self.system_prompt = SOLVE_SYSTEM_PROMPT
        self.temperature = 0.6  # Plus précis
```

#### **Prompts Système Spécialisés**
```python
PROPOSE_SYSTEM_PROMPT = """
Tu es un créateur de tâches d'apprentissage. Ton objectif est de générer 
des problèmes de programmation qui sont :
- Ni trop faciles (>90% de réussite)
- Ni impossibles (<10% de réussite)  
- Créatifs et diversifiés
- Vérifiables automatiquement
"""

SOLVE_SYSTEM_PROMPT = """
Tu es un résolveur de problèmes. Ton objectif est de :
- Comprendre précisément la tâche
- Développer une solution étape par étape
- Écrire du code correct et efficace
- Expliquer ton raisonnement
"""
```

---

## 🌍 **ENVIRONNEMENT D'EXÉCUTION**

### 🐍 **Python Executor - Cœur Vérifiable**

#### **Architecture Sécurisée**
```python
class SecurePythonExecutor:
    def __init__(self):
        self.sandbox = CodeSandbox()
        self.timeout = 5.0  # secondes
        self.memory_limit = 128  # MB
        
    def execute(self, code: str, input_data: str = "") -> ExecutionResult:
        """Exécute du code Python de manière sécurisée"""
        
        # 1. Validation de sécurité
        if not self._is_safe_code(code):
            return ExecutionResult(error="Code non sécurisé détecté")
        
        # 2. Préparation de l'environnement
        env = self._create_sandbox_environment()
        
        # 3. Exécution avec limites
        try:
            with timeout(self.timeout):
                result = env.run(code, input_data)
            return ExecutionResult(
                success=True,
                output=result.stdout,
                execution_time=result.time
            )
        except TimeoutError:
            return ExecutionResult(error="Timeout dépassé")
        except MemoryError:
            return ExecutionResult(error="Limite mémoire dépassée")
        except Exception as e:
            return ExecutionResult(error=f"Erreur d'exécution: {e}")
```

#### **Restrictions de Sécurité**
```python
FORBIDDEN_IMPORTS = {
    'os', 'sys', 'subprocess', 'socket', 'urllib',
    'requests', 'http', 'ftplib', 'smtplib'
}

FORBIDDEN_BUILTINS = {
    'eval', 'exec', 'compile', 'open', '__import__'
}

ALLOWED_BUILTINS = {
    'len', 'range', 'enumerate', 'zip', 'map', 'filter',
    'sum', 'max', 'min', 'abs', 'round', 'sorted', 'reversed'
}
```

### ✅ **Validator - Vérification Objective**

#### **Types de Validation**
```python
class TaskValidator:
    def validate_triplet(self, program: str, input_data: str, 
                        expected_output: str) -> bool:
        """Valide qu'un triplet (P,I,O) est cohérent"""
        
        # Exécution du programme
        result = self.executor.execute(program, input_data)
        
        if not result.success:
            return False
            
        # Comparaison avec égalité de valeur Python
        try:
            actual = eval(result.output.strip())
            expected = eval(expected_output.strip())
            return actual == expected
        except:
            # Fallback sur comparaison de chaînes
            return result.output.strip() == expected_output.strip()
    
    def validate_solution(self, solution: str, ground_truth: str) -> bool:
        """Valide une solution contre la vérité terrain"""
        return self.validate_triplet(
            program=solution,
            input_data="",  # Pas d'input pour les solutions
            expected_output=ground_truth
        )
```

### 📊 **Metrics Calculator - Métriques de Qualité**

#### **Métriques de Learnability**
```python
class LearnabilityCalculator:
    def calculate_difficulty(self, task: Task, model: AZRModel, 
                           n_trials: int = 5) -> float:
        """Calcule la difficulté d'une tâche"""
        
        successes = 0
        for _ in range(n_trials):
            solution = model.solve_task(task)
            if self.validator.validate_solution(solution, task.ground_truth):
                successes += 1
        
        return successes / n_trials
    
    def calculate_learnability_reward(self, difficulty: float) -> float:
        """Calcule la récompense de learnability"""
        
        if difficulty == 0.0 or difficulty == 1.0:
            return 0.0  # Trop facile ou impossible
        else:
            return 1.0 - difficulty  # Difficulté optimale récompensée
```

#### **Métriques de Diversité**
```python
class DiversityCalculator:
    def calculate_task_distance(self, task1: Task, task2: Task) -> float:
        """Calcule la distance entre deux tâches"""
        
        # Extraction de features
        features1 = self._extract_features(task1)
        features2 = self._extract_features(task2)
        
        # Distance euclidienne normalisée
        return np.linalg.norm(features1 - features2)
    
    def _extract_features(self, task: Task) -> np.ndarray:
        """Extrait des features d'une tâche"""
        features = []
        
        # Longueur du programme
        features.append(len(task.program))
        
        # Complexité cyclomatique
        features.append(self._cyclomatic_complexity(task.program))
        
        # Nombre de variables
        features.append(self._count_variables(task.program))
        
        # Type de structures de contrôle
        features.extend(self._control_structures(task.program))
        
        return np.array(features)
```

---

## 💾 **SYSTÈME DE MÉMOIRE**

### 📚 **Task Buffer - Mémoire des Tâches**

#### **Structure de Données**
```python
class TaskBuffer:
    def __init__(self, max_size: int = 1000):
        self.buffer = deque(maxlen=max_size)
        self.index = {}  # Index pour recherche rapide
        
    def add_task(self, task: Task, metadata: dict):
        """Ajoute une tâche avec ses métadonnées"""
        
        entry = TaskEntry(
            task=task,
            timestamp=time.time(),
            difficulty=metadata.get('difficulty', 0.0),
            diversity=metadata.get('diversity', 0.0),
            success_rate=metadata.get('success_rate', 0.0)
        )
        
        self.buffer.append(entry)
        self._update_index(entry)
    
    def sample_context(self, task_type: str, k: int = 5) -> List[Task]:
        """Échantillonne des tâches pour le contexte"""
        
        # Filtrage par type
        candidates = [entry for entry in self.buffer 
                     if entry.task.type == task_type]
        
        # Échantillonnage pondéré par qualité
        weights = [entry.difficulty * entry.diversity for entry in candidates]
        
        return random.choices(candidates, weights=weights, k=min(k, len(candidates)))
```

### 📈 **History Tracker - Suivi des Performances**

#### **Métriques Historiques**
```python
class HistoryTracker:
    def __init__(self):
        self.metrics_history = defaultdict(list)
        self.model_checkpoints = []
        
    def log_iteration(self, iteration: int, metrics: dict):
        """Enregistre les métriques d'une itération"""
        
        timestamp = time.time()
        
        for metric_name, value in metrics.items():
            self.metrics_history[metric_name].append({
                'iteration': iteration,
                'timestamp': timestamp,
                'value': value
            })
    
    def get_trend(self, metric_name: str, window: int = 100) -> float:
        """Calcule la tendance d'une métrique"""
        
        recent_values = self.metrics_history[metric_name][-window:]
        if len(recent_values) < 2:
            return 0.0
            
        # Régression linéaire simple
        x = np.array([entry['iteration'] for entry in recent_values])
        y = np.array([entry['value'] for entry in recent_values])
        
        slope, _ = np.polyfit(x, y, 1)
        return slope
```

---

## ⚙️ **SYSTÈME DE CONTRÔLE**

### 🎛️ **Controller - Orchestrateur Principal**

#### **Boucle d'Entraînement**
```python
class AZRController:
    def __init__(self, model: AZRModel, config: AZRConfig):
        self.model = model
        self.config = config
        self.executor = SecurePythonExecutor()
        self.validator = TaskValidator()
        self.task_buffer = TaskBuffer()
        self.history = HistoryTracker()
        
    def training_step(self) -> dict:
        """Une étape complète d'entraînement AZR"""
        
        metrics = {}
        
        # 1. Phase de Proposition
        propose_metrics = self._propose_phase()
        metrics.update(propose_metrics)
        
        # 2. Phase de Résolution
        solve_metrics = self._solve_phase()
        metrics.update(solve_metrics)
        
        # 3. Phase de Mise à Jour
        update_metrics = self._update_phase()
        metrics.update(update_metrics)
        
        # 4. Logging et Monitoring
        self.history.log_iteration(self.current_iteration, metrics)
        
        return metrics
    
    def _propose_phase(self) -> dict:
        """Phase de proposition de tâches"""
        
        proposed_tasks = []
        propose_rewards = []
        
        for task_type in ['deduction', 'induction', 'abduction']:
            # Échantillonnage du contexte
            context = self.task_buffer.sample_context(task_type, k=5)
            
            # Génération de nouvelles tâches
            new_tasks = self.model.propose_tasks(
                task_type=task_type,
                context=context,
                n_tasks=self.config.batch_size // 3
            )
            
            # Validation et calcul des récompenses
            for task in new_tasks:
                if self.validator.validate_triplet(task.program, task.input, task.output):
                    proposed_tasks.append(task)
                    
                    # Calcul de la récompense de learnability
                    difficulty = self.metrics.calculate_difficulty(task, self.model)
                    diversity = self.metrics.calculate_diversity(task, self.task_buffer)
                    
                    reward = self._calculate_propose_reward(difficulty, diversity)
                    propose_rewards.append(reward)
        
        return {
            'proposed_tasks': len(proposed_tasks),
            'avg_propose_reward': np.mean(propose_rewards) if propose_rewards else 0.0
        }
```

### 🔄 **Optimizer - Mise à Jour des Paramètres**

#### **Algorithme REINFORCE++ Adapté**
```python
class AZROptimizer:
    def __init__(self, model: AZRModel, learning_rate: float = 1e-6):
        self.model = model
        self.optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        self.baseline_propose = 0.0
        self.baseline_solve = 0.0
        
    def update_model(self, batch_data: dict):
        """Met à jour le modèle avec les données de batch"""
        
        total_loss = 0.0
        
        # Gradient pour le rôle proposeur
        propose_loss = self._calculate_propose_loss(batch_data)
        total_loss += propose_loss
        
        # Gradient pour le rôle résolveur
        solve_loss = self._calculate_solve_loss(batch_data)
        total_loss += solve_loss
        
        # Backpropagation
        self.optimizer.zero_grad()
        total_loss.backward()
        
        # Gradient clipping pour stabilité
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
        
        self.optimizer.step()
        
        # Mise à jour des baselines
        self._update_baselines(batch_data)
        
        return {
            'total_loss': total_loss.item(),
            'propose_loss': propose_loss.item(),
            'solve_loss': solve_loss.item()
        }
```

---

## 🎓 **RÉCAPITULATIF DU MODULE**

### ✅ **Ce que vous avez appris :**

1. **Architecture complète** : Composants et interactions
2. **Modèle unifié** : LLM avec rôles duaux
3. **Environnement d'exécution** : Sécurisé et vérifiable
4. **Système de mémoire** : Buffer et historique
5. **Contrôle et optimisation** : Orchestration et mise à jour

### 🎯 **Prochaine étape :**
[Module 2.2 - Système de Récompenses](./02_Systeme_Recompenses.md) pour comprendre les mécanismes d'évaluation.

---

**🎉 Excellent ! Vous comprenez maintenant l'architecture complète d'un système AZR !**
