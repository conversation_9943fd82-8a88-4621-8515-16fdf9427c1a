#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 TEST DE SYNCHRONISATION DES 5 INDICES AZR

Script de test pour vérifier que les 5 indices sont parfaitement synchronisés :
1. Index IMPAIR/PAIR
2. Index DESYNC/SYNC  
3. Index COMBINÉ
4. Index P/B/T
5. Index S/O

Utilise l'interface graphique avec panneau de debug pour validation visuelle.
"""

import sys
import os
import logging
from typing import Dict, List, Any

# Ajouter le répertoire parent au path pour importer le module principal
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azr_baccarat_predictor import create_azr_interface, AZRConfig, logger

def test_sequence_predefined():
    """
    Test avec une séquence prédéfinie pour vérifier la synchronisation
    
    Séquence de test : 10 manches avec patterns variés
    """
    print("🔍 TEST DE SYNCHRONISATION DES 5 INDICES")
    print("=" * 50)
    
    # Séquence de test prédéfinie
    test_sequence = [
        # <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Parité attendue, SY<PERSON> attendu, Combiné attendu, S/O attendu
        (1, "PLAYER", "IMPAIR", "DESYNC", "IMPAIR_DESYNC", "--"),      # Première manche
        (2, "BANKER", "PAIR", "SYNC", "PAIR_SYNC", "O"),               # P→B = Opposite
        (3, "PLAYER", "IMPAIR", "DESYNC", "IMPAIR_DESYNC", "O"),       # B→P = Opposite  
        (4, "PLAYER", "PAIR", "SYNC", "PAIR_SYNC", "S"),               # P→P = Same
        (5, "BANKER", "IMPAIR", "DESYNC", "IMPAIR_DESYNC", "O"),       # P→B = Opposite
        (6, "TIE", "PAIR", "SYNC", "PAIR_SYNC", "--"),                 # Tie = pas de S/O
        (7, "BANKER", "IMPAIR", "DESYNC", "IMPAIR_DESYNC", "S"),       # B→B = Same (vs manche 5)
        (8, "PLAYER", "PAIR", "SYNC", "PAIR_SYNC", "O"),               # B→P = Opposite
        (9, "PLAYER", "IMPAIR", "DESYNC", "IMPAIR_DESYNC", "S"),       # P→P = Same
        (10, "BANKER", "PAIR", "SYNC", "PAIR_SYNC", "O")               # P→B = Opposite
    ]
    
    print("\n📊 SÉQUENCE DE TEST PRÉDÉFINIE :")
    print("Manche | Résultat | Parité   | SYNC     | Combiné        | S/O")
    print("-" * 65)
    for manche, resultat, parite, sync, combine, so in test_sequence:
        print(f"{manche:6} | {resultat:8} | {parite:8} | {sync:8} | {combine:14} | {so:3}")
    
    print(f"\n🎯 INSTRUCTIONS POUR LE TEST :")
    print("1. L'interface va s'ouvrir avec le panneau de debug des 5 indices")
    print("2. Initialisez d'abord les cartes brûlées (PAIR pour commencer en SYNC)")
    print("3. Jouez la séquence ci-dessus manche par manche")
    print("4. Vérifiez que chaque index correspond exactement aux valeurs attendues")
    print("5. Les séquences complètes doivent s'afficher en temps réel")
    
    print(f"\n⚠️  POINTS DE VÉRIFICATION CRITIQUES :")
    print("- Index 1 (IMPAIR/PAIR) : Position et type corrects")
    print("- Index 2 (DESYNC/SYNC) : État et transition corrects")  
    print("- Index 3 (COMBINÉ) : Combinaison des 2 premiers")
    print("- Index 4 (P/B/T) : Résultat et numéro de manche")
    print("- Index 5 (S/O) : Conversion vs manche P/B précédente")
    
    print(f"\n🚨 INCOHÉRENCES À DÉTECTER :")
    print("- Décalage entre les indices")
    print("- Mauvais calcul de SYNC/DESYNC")
    print("- Erreur dans l'index combiné")
    print("- S/O incorrect vs résultat précédent")
    print("- Longueurs de séquences différentes")
    
    return test_sequence

def test_edge_cases():
    """
    Test des cas limites pour détecter les incohérences
    """
    edge_cases = [
        # Cas avec plusieurs Ties consécutifs
        "🎯 CAS 1 - TIES CONSÉCUTIFS :",
        "Vérifier que les Ties n'affectent pas IMPAIR/PAIR et SYNC/DESYNC",
        "mais ne génèrent pas de S/O",
        "",
        # Cas avec alternance rapide
        "🎯 CAS 2 - ALTERNANCE RAPIDE P/B :",
        "Player→Banker→Player→Banker (toutes S/O = O)",
        "",
        # Cas avec séquences longues identiques
        "🎯 CAS 3 - SÉQUENCES IDENTIQUES :",
        "Player→Player→Player→Player (toutes S/O = S après la première)",
        "",
        # Cas de transition SYNC/DESYNC
        "🎯 CAS 4 - TRANSITIONS SYNC/DESYNC :",
        "Vérifier que IMPAIR change toujours l'état SYNC/DESYNC",
        "et que PAIR maintient l'état"
    ]
    
    print("\n🧪 CAS LIMITES À TESTER :")
    print("=" * 40)
    for case in edge_cases:
        print(case)

def validate_synchronization_rules():
    """
    Règles de validation de la synchronisation
    """
    rules = [
        "📋 RÈGLES DE SYNCHRONISATION À VÉRIFIER :",
        "",
        "✅ RÈGLE 1 - Index IMPAIR/PAIR :",
        "   Position impaire (1,3,5,7...) → IMPAIR",
        "   Position paire (2,4,6,8...) → PAIR",
        "",
        "✅ RÈGLE 2 - Index DESYNC/SYNC :",
        "   IMPAIR → Change l'état (SYNC→DESYNC ou DESYNC→SYNC)",
        "   PAIR → Maintient l'état",
        "",
        "✅ RÈGLE 3 - Index COMBINÉ :",
        "   Combinaison exacte : IMPAIR_SYNC, IMPAIR_DESYNC, PAIR_SYNC, PAIR_DESYNC",
        "",
        "✅ RÈGLE 4 - Index P/B/T :",
        "   Résultat exact de la manche",
        "   Numéro de manche P/B correct (Ties ne comptent pas)",
        "",
        "✅ RÈGLE 5 - Index S/O :",
        "   Première manche P/B → '--'",
        "   Manches suivantes → 'S' si même résultat, 'O' si différent",
        "   Ties → '--' (pas de comparaison)",
        "",
        "🚨 INCOHÉRENCES POSSIBLES :",
        "   - Décalage d'index entre les séquences",
        "   - Mauvais calcul SYNC/DESYNC",
        "   - S/O comparé au mauvais résultat",
        "   - Longueurs de séquences différentes",
        "   - Index combiné incorrect"
    ]
    
    print("\n" + "\n".join(rules))

def main():
    """Fonction principale du test"""
    print("🎯 LANCEMENT DU TEST DE SYNCHRONISATION")
    print("=" * 50)
    
    # Afficher les règles de validation
    validate_synchronization_rules()
    
    # Afficher la séquence de test
    test_sequence = test_sequence_predefined()
    
    # Afficher les cas limites
    test_edge_cases()
    
    print(f"\n🚀 LANCEMENT DE L'INTERFACE AVEC DEBUG...")
    print("Utilisez le panneau de debug pour vérifier la synchronisation !")
    
    try:
        # Créer et lancer l'interface avec debug
        interface = create_azr_interface()
        interface.run()
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement : {e}")
        print("Vérifiez que tkinter est installé et disponible.")
    
    print(f"\n✅ Test terminé. Vérifiez les logs pour d'éventuelles incohérences.")

if __name__ == "__main__":
    # Configuration du logging pour le test
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_synchronisation.log', encoding='utf-8')
        ]
    )
    
    main()
