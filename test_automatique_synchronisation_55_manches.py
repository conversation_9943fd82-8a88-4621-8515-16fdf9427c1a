#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 TEST AUTOMATIQUE DE SYNCHRONISATION - 55 MANCHES

Test automatique complet qui simule une partie de 55 manches pour vérifier
la synchronisation parfaite des 5 indices AZR :

1. Index IMPAIR/PAIR
2. Index DESYNC/SYNC  
3. Index COMBINÉ
4. Index P/B/T
5. Index S/O

Le test génère automatiquement une séquence de 55 manches, l'exécute,
et vérifie que tous les indices sont parfaitement synchronisés.
"""

import sys
import os
import random
import logging
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azr_baccarat_predictor import AZRBaccaratPredictor, AZRConfig, BaccaratHand

@dataclass
class TestResult:
    """Résultat d'un test de synchronisation"""
    manche: int
    success: bool
    expected: Dict[str, Any]
    actual: Dict[str, Any]
    errors: List[str]

class TestSynchronisation55Manches:
    """Test automatique de synchronisation sur 55 manches"""
    
    def __init__(self):
        self.predictor = None
        self.test_results: List[TestResult] = []
        self.sequence_55_manches = []
        self.expected_indices = {
            'impair_pair': [],
            'desync_sync': [],
            'combined': [],
            'pbt': [],
            'so': []
        }
        
    def generate_test_sequence(self) -> List[Dict[str, Any]]:
        """
        Génère une séquence de test de 55 manches avec patterns variés
        
        Returns:
            Liste de 55 manches avec résultats et parités
        """
        print("🎲 Génération de la séquence de test de 55 manches...")
        
        # Résultats possibles avec probabilités réalistes
        results = ['PLAYER', 'BANKER', 'TIE']
        weights = [45, 50, 5]  # Probabilités approximatives du Baccarat
        
        sequence = []
        
        for manche in range(1, 56):  # 55 manches
            # Génération aléatoire pondérée
            result = random.choices(results, weights=weights)[0]
            
            # Ajout de patterns spéciaux pour tester la synchronisation
            if manche <= 5:
                # Début avec pattern connu
                pattern_results = ['PLAYER', 'BANKER', 'PLAYER', 'PLAYER', 'BANKER']
                result = pattern_results[manche - 1]
            elif 20 <= manche <= 25:
                # Séquence de Ties pour tester la gestion
                if manche % 2 == 0:
                    result = 'TIE'
            elif 40 <= manche <= 45:
                # Alternance stricte P/B
                result = 'PLAYER' if manche % 2 == 0 else 'BANKER'
            
            sequence.append({
                'manche': manche,
                'result': result,
                'parity': 'IMPAIR' if manche % 2 == 1 else 'PAIR'
            })
        
        self.sequence_55_manches = sequence
        print(f"✅ Séquence générée : {len(sequence)} manches")
        return sequence
    
    def calculate_expected_indices(self):
        """
        Calcule les valeurs attendues pour les 5 indices
        """
        print("🧮 Calcul des indices attendus...")
        
        current_sync_state = "SYNC"  # État initial après brûlage PAIR
        previous_pbt_result = None
        
        for i, manche_data in enumerate(self.sequence_55_manches):
            manche = manche_data['manche']
            result = manche_data['result']
            parity = manche_data['parity']
            
            # Index 1: IMPAIR/PAIR
            self.expected_indices['impair_pair'].append(parity)
            
            # Index 2: DESYNC/SYNC
            if parity == 'IMPAIR':
                # IMPAIR change l'état
                current_sync_state = "DESYNC" if current_sync_state == "SYNC" else "SYNC"
            # PAIR maintient l'état
            self.expected_indices['desync_sync'].append(current_sync_state)
            
            # Index 3: COMBINÉ
            combined = f"{parity}_{current_sync_state}"
            self.expected_indices['combined'].append(combined)
            
            # Index 4: P/B/T
            self.expected_indices['pbt'].append(result)
            
            # Index 5: S/O
            if result == 'TIE':
                so_conversion = '--'
            elif previous_pbt_result is None:
                so_conversion = '--'  # Première manche P/B
            elif previous_pbt_result == result:
                so_conversion = 'S'   # Same
            else:
                so_conversion = 'O'   # Opposite
            
            self.expected_indices['so'].append(so_conversion)
            
            # Mettre à jour le résultat précédent (seulement P/B)
            if result in ['PLAYER', 'BANKER']:
                previous_pbt_result = result
        
        print("✅ Indices attendus calculés")
    
    def setup_predictor(self):
        """Initialise le prédicteur AZR"""
        print("⚙️ Initialisation du prédicteur AZR...")

        config = AZRConfig()
        self.predictor = AZRBaccaratPredictor(config)

        # Initialisation du brûlage (PAIR pour commencer en SYNC)
        self.predictor.set_burn_parity('PAIR')

        print("✅ Prédicteur initialisé")
    
    def execute_test_sequence(self):
        """
        Exécute la séquence de test et vérifie la synchronisation
        """
        print("🚀 Exécution de la séquence de test...")

        # État de synchronisation actuel (commence en SYNC après brûlage PAIR)
        current_sync_state = "SYNC"
        previous_pbt_result = None

        for i, manche_data in enumerate(self.sequence_55_manches):
            manche = manche_data['manche']
            result = manche_data['result']
            parity = manche_data['parity']

            print(f"  Manche {manche:2d}: {result:7} {parity:6}", end=" → ")

            # Calculer l'état SYNC/DESYNC
            if parity == 'IMPAIR':
                # IMPAIR change l'état
                current_sync_state = "DESYNC" if current_sync_state == "SYNC" else "SYNC"
            # PAIR maintient l'état

            # Calculer la conversion S/O
            so_conversion = '--'
            if result in ['PLAYER', 'BANKER']:
                if previous_pbt_result is not None:
                    so_conversion = 'S' if previous_pbt_result == result else 'O'
                previous_pbt_result = result

            # Exécuter la manche
            try:
                hand_data = {
                    'pb_hand_number': manche,
                    'result': result,
                    'parity': parity,
                    'sync_state': current_sync_state,  # ✅ Calculé correctement
                    'so_conversion': so_conversion     # ✅ Calculé correctement
                }

                prediction = self.predictor.receive_hand_data(hand_data)

                # Récupérer les indices actuels
                actual_indices = self._get_current_indices()

                # Vérifier la synchronisation
                test_result = self._verify_synchronization(manche, actual_indices)
                self.test_results.append(test_result)

                if test_result.success:
                    print("✅")
                else:
                    print(f"❌ {len(test_result.errors)} erreurs")
                    for error in test_result.errors:
                        print(f"    ⚠️ {error}")

            except Exception as e:
                print(f"💥 ERREUR: {e}")
                test_result = TestResult(
                    manche=manche,
                    success=False,
                    expected={},
                    actual={},
                    errors=[f"Exception: {e}"]
                )
                self.test_results.append(test_result)
    
    def _get_current_indices(self) -> Dict[str, List[str]]:
        """Récupère les indices actuels depuis le prédicteur"""
        try:
            indices = {
                'impair_pair': [],
                'desync_sync': [],
                'combined': [],
                'pbt': [],
                'so': []
            }
            
            for hand in self.predictor.hands_history:
                indices['impair_pair'].append(hand.parity)
                indices['desync_sync'].append(hand.sync_state)
                indices['combined'].append(hand.combined_state)
                indices['pbt'].append(hand.result)
                if hand.so_conversion in ['S', 'O', '--']:
                    indices['so'].append(hand.so_conversion)
            
            return indices
            
        except Exception as e:
            print(f"⚠️ Erreur récupération indices: {e}")
            return {'impair_pair': [], 'desync_sync': [], 'combined': [], 'pbt': [], 'so': []}
    
    def _verify_synchronization(self, manche: int, actual_indices: Dict[str, List[str]]) -> TestResult:
        """
        Vérifie la synchronisation pour une manche donnée
        """
        errors = []
        index = manche - 1  # Index 0-based
        
        # Vérifier chaque index
        expected_data = {}
        actual_data = {}
        
        # Index 1: IMPAIR/PAIR
        if index < len(self.expected_indices['impair_pair']) and index < len(actual_indices['impair_pair']):
            expected_ip = self.expected_indices['impair_pair'][index]
            actual_ip = actual_indices['impair_pair'][index]
            expected_data['impair_pair'] = expected_ip
            actual_data['impair_pair'] = actual_ip
            if expected_ip != actual_ip:
                errors.append(f"IMPAIR/PAIR: attendu={expected_ip}, réel={actual_ip}")
        
        # Index 2: DESYNC/SYNC
        if index < len(self.expected_indices['desync_sync']) and index < len(actual_indices['desync_sync']):
            expected_ds = self.expected_indices['desync_sync'][index]
            actual_ds = actual_indices['desync_sync'][index]
            expected_data['desync_sync'] = expected_ds
            actual_data['desync_sync'] = actual_ds
            if expected_ds != actual_ds:
                errors.append(f"DESYNC/SYNC: attendu={expected_ds}, réel={actual_ds}")
        
        # Index 3: COMBINÉ
        if index < len(self.expected_indices['combined']) and index < len(actual_indices['combined']):
            expected_cb = self.expected_indices['combined'][index]
            actual_cb = actual_indices['combined'][index]
            expected_data['combined'] = expected_cb
            actual_data['combined'] = actual_cb
            if expected_cb != actual_cb:
                errors.append(f"COMBINÉ: attendu={expected_cb}, réel={actual_cb}")
        
        # Index 4: P/B/T
        if index < len(self.expected_indices['pbt']) and index < len(actual_indices['pbt']):
            expected_pbt = self.expected_indices['pbt'][index]
            actual_pbt = actual_indices['pbt'][index]
            expected_data['pbt'] = expected_pbt
            actual_data['pbt'] = actual_pbt
            if expected_pbt != actual_pbt:
                errors.append(f"P/B/T: attendu={expected_pbt}, réel={actual_pbt}")
        
        # Index 5: S/O
        so_index = len([r for r in self.expected_indices['pbt'][:index+1] if r in ['PLAYER', 'BANKER']]) - 1
        if so_index >= 0 and so_index < len(self.expected_indices['so']) and so_index < len(actual_indices['so']):
            expected_so = self.expected_indices['so'][so_index]
            actual_so = actual_indices['so'][so_index]
            expected_data['so'] = expected_so
            actual_data['so'] = actual_so
            if expected_so != actual_so:
                errors.append(f"S/O: attendu={expected_so}, réel={actual_so}")
        
        # Vérifier les longueurs
        for index_name in ['impair_pair', 'desync_sync', 'combined', 'pbt']:
            expected_len = len(self.expected_indices[index_name][:index+1])
            actual_len = len(actual_indices[index_name])
            if expected_len != actual_len:
                errors.append(f"Longueur {index_name}: attendu={expected_len}, réel={actual_len}")
        
        return TestResult(
            manche=manche,
            success=len(errors) == 0,
            expected=expected_data,
            actual=actual_data,
            errors=errors
        )
    
    def generate_report(self):
        """
        Génère un rapport détaillé des résultats de test
        """
        print("\n" + "="*80)
        print("📊 RAPPORT DE TEST DE SYNCHRONISATION - 55 MANCHES")
        print("="*80)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r.success])
        failed_tests = total_tests - successful_tests
        
        print(f"\n📈 RÉSULTATS GLOBAUX :")
        print(f"   Total manches testées : {total_tests}")
        print(f"   ✅ Synchronisées      : {successful_tests}")
        print(f"   ❌ Désynchronisées    : {failed_tests}")
        print(f"   📊 Taux de réussite   : {(successful_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n🚨 ERREURS DÉTECTÉES :")
            for result in self.test_results:
                if not result.success:
                    print(f"   Manche {result.manche:2d}: {len(result.errors)} erreur(s)")
                    for error in result.errors:
                        print(f"     • {error}")
        
        # Vérification des longueurs finales
        print(f"\n📏 LONGUEURS FINALES DES SÉQUENCES :")
        actual_indices = self._get_current_indices()
        for index_name in ['impair_pair', 'desync_sync', 'combined', 'pbt', 'so']:
            expected_len = len(self.expected_indices[index_name])
            actual_len = len(actual_indices[index_name])
            status = "✅" if expected_len == actual_len else "❌"
            print(f"   {index_name:12}: attendu={expected_len:2d}, réel={actual_len:2d} {status}")
        
        # Affichage des dernières valeurs pour vérification
        print(f"\n🔍 DERNIÈRES VALEURS (manches 51-55) :")
        print("Manche | IMPAIR/PAIR | DESYNC/SYNC | COMBINÉ        | P/B/T   | S/O")
        print("-" * 70)
        for i in range(50, min(55, len(self.expected_indices['impair_pair']))):
            manche = i + 1
            ip = self.expected_indices['impair_pair'][i] if i < len(self.expected_indices['impair_pair']) else '?'
            ds = self.expected_indices['desync_sync'][i] if i < len(self.expected_indices['desync_sync']) else '?'
            cb = self.expected_indices['combined'][i] if i < len(self.expected_indices['combined']) else '?'
            pbt = self.expected_indices['pbt'][i] if i < len(self.expected_indices['pbt']) else '?'
            
            # S/O index est différent car basé sur P/B seulement
            so_idx = len([r for r in self.expected_indices['pbt'][:i+1] if r in ['PLAYER', 'BANKER']]) - 1
            so = self.expected_indices['so'][so_idx] if so_idx >= 0 and so_idx < len(self.expected_indices['so']) else '?'
            
            print(f"{manche:6} | {ip:11} | {ds:11} | {cb:14} | {pbt:7} | {so:3}")
        
        return successful_tests == total_tests
    
    def run_test(self) -> bool:
        """
        Exécute le test complet de synchronisation
        
        Returns:
            True si tous les tests passent, False sinon
        """
        print("🤖 DÉMARRAGE DU TEST AUTOMATIQUE DE SYNCHRONISATION")
        print("=" * 60)
        
        try:
            # 1. Générer la séquence de test
            self.generate_test_sequence()
            
            # 2. Calculer les indices attendus
            self.calculate_expected_indices()
            
            # 3. Initialiser le prédicteur
            self.setup_predictor()
            
            # 4. Exécuter la séquence
            self.execute_test_sequence()
            
            # 5. Générer le rapport
            success = self.generate_report()
            
            if success:
                print(f"\n🎉 SUCCÈS : Tous les indices sont parfaitement synchronisés !")
            else:
                print(f"\n⚠️ ÉCHEC : Des désynchronisations ont été détectées.")
            
            return success
            
        except Exception as e:
            print(f"\n💥 ERREUR CRITIQUE : {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Fonction principale"""
    # Configuration du logging
    logging.basicConfig(
        level=logging.WARNING,  # Réduire le bruit
        format='%(levelname)s - %(message)s'
    )
    
    # Fixer la seed pour reproductibilité
    random.seed(42)
    
    # Lancer le test
    test = TestSynchronisation55Manches()
    success = test.run_test()
    
    # Code de sortie
    exit_code = 0 if success else 1
    print(f"\n🏁 Test terminé avec code de sortie : {exit_code}")
    return exit_code

if __name__ == "__main__":
    exit(main())
