# 📐 MODULE 3.1 - FORMULES FONDAMENTALES

## 🎯 **OBJECTIFS DU MODULE**

À la fin de ce module, vous maîtriserez :
- ✅ La fonction objectif principale d'AZR et ses composants
- ✅ Les formules de récompenses et leur justification théorique
- ✅ Les métriques de learnability et diversité
- ✅ Les équations de mise à jour des paramètres

---

## 🧮 **FONCTION OBJECTIF PRINCIPALE**

### 🎯 **Formule Fondamentale AZR**

La fonction objectif d'AZR unifie proposition et résolution dans une seule équation élégante :

```math
J(θ) = max_θ E_{z∼p(z)} [
    E_{(x,y*)∼f_e(·|τ),τ∼π_θ^{propose}(·|z)} [
        r_e^{propose}(τ,π_θ) + λ E_{y∼π_θ^{solve}(·|x)} [r_e^{solve}(y,y*)]
    ]
]
```

### 🔍 **Décomposition Mathématique**

#### **Espérance Externe sur z**
```math
E_{z∼p(z)} [·]
```
- **z** : Variable de contexte (échantillon de tâches passées)
- **p(z)** : Distribution de probabilité sur les contextes
- **Rôle** : Assure la diversité des contextes d'apprentissage

#### **Espérance sur les Tâches Proposées**
```math
E_{(x,y*)∼f_e(·|τ),τ∼π_θ^{propose}(·|z)} [·]
```
- **τ** : Tâche proposée (triplet programme-input-output)
- **π_θ^{propose}** : Politique de proposition paramétrée par θ
- **f_e** : Fonction de l'environnement qui transforme τ en (x,y*)
- **x** : Question extraite de la tâche
- **y*** : Réponse correcte vérifiée

#### **Terme de Récompense de Proposition**
```math
r_e^{propose}(τ,π_θ)
```
- **Mesure** : Potentiel d'apprentissage de la tâche τ
- **Dépendance** : Fonction de τ et des capacités actuelles π_θ

#### **Terme de Récompense de Résolution**
```math
λ E_{y∼π_θ^{solve}(·|x)} [r_e^{solve}(y,y*)]
```
- **λ** : Coefficient d'équilibrage (typiquement λ = 1.0)
- **y** : Réponse générée par le résolveur
- **r_e^{solve}** : Récompense de correctness

---

## 🏆 **FORMULES DE RÉCOMPENSES**

### 🎯 **Récompense de Learnability**

#### **Formule de Base**
```math
r_e^{propose}(τ,π_θ) = \begin{cases}
0 & \text{si } \bar{r}_{solve} = 0 \text{ ou } \bar{r}_{solve} = 1 \\
1 - \bar{r}_{solve} & \text{sinon}
\end{cases}
```

#### **Calcul du Taux de Réussite Moyen**
```math
\bar{r}_{solve} = \frac{1}{n} \sum_{i=1}^{n} r_e^{solve}(y_i, y*)
```

Où :
- **n** : Nombre d'essais (typiquement n = 5)
- **y_i** : i-ème tentative de solution
- **r_e^{solve}(y_i, y*)** : Récompense binaire pour la i-ème tentative

#### **Justification Théorique**

La fonction de learnability est conçue pour maximiser l'apprentissage selon la **Zone de Développement Proximal** :

```math
\text{Apprentissage}(τ) = \begin{cases}
0 & \text{si tâche trop facile} \\
\text{Maximum} & \text{si difficulté optimale} \\
0 & \text{si tâche impossible}
\end{cases}
```

**Graphique de la Fonction de Learnability :**
```
Récompense
    ↑
1.0 |    ╭─╮
    |   ╱   ╲
0.5 |  ╱     ╲
    | ╱       ╲
0.0 |╱_________╲___→ Taux de Réussite
    0  0.2  0.5  0.8  1.0
```

### ✅ **Récompense de Correctness**

#### **Formule Binaire**
```math
r_e^{solve}(y,y*) = \mathbb{I}(y = y*)
```

Où **𝕀** est la fonction indicatrice :
```math
\mathbb{I}(y = y*) = \begin{cases}
1 & \text{si } y = y* \\
0 & \text{si } y ≠ y*
\end{cases}
```

#### **Égalité Sémantique en Python**
Pour le code Python, l'égalité est évaluée sémantiquement :
```math
y = y* \iff \text{eval}(y) = \text{eval}(y*)
```

**Exemples :**
- `"[1, 2, 3]"` = `"[1,2,3]"` → **Vrai**
- `"2 + 2"` = `"4"` → **Vrai**
- `"def f(x): return x*2"` ≠ `"lambda x: x*2"` → **Faux** (syntaxe différente)

---

## 📊 **MÉTRIQUES DE QUALITÉ**

### 🎲 **Score de Diversité**

#### **Distance entre Tâches**
```math
d(τ_1, τ_2) = ||φ(τ_1) - φ(τ_2)||_2
```

Où **φ(τ)** est la fonction d'extraction de features :
```math
φ(τ) = [
    \text{length}(τ), 
    \text{complexity}(τ), 
    \text{variables}(τ), 
    \text{structures}(τ)
]
```

#### **Score de Diversité Global**
```math
\text{diversity}(τ_{new}, T_{existing}) = \frac{1}{k} \sum_{i=1}^{k} d(τ_{new}, τ_i)
```

Où **T_{existing}** sont les k dernières tâches du buffer.

### 🔧 **Complexité Cyclomatique**

#### **Formule de McCabe**
```math
CC(P) = E - N + 2P
```

Où :
- **E** : Nombre d'arêtes dans le graphe de contrôle
- **N** : Nombre de nœuds dans le graphe de contrôle  
- **P** : Nombre de composants connexes (typiquement P = 1)

#### **Calcul Pratique pour Python**
```math
CC(P) = 1 + \sum_{s \in S} w(s)
```

Où **S** est l'ensemble des structures de contrôle et **w(s)** leurs poids :
- `if`, `elif` : w = 1
- `for`, `while` : w = 1
- `and`, `or` : w = 1
- `except` : w = 1

### 📏 **Métriques de Halstead**

#### **Vocabulaire et Longueur**
```math
\begin{align}
n &= n_1 + n_2 \quad \text{(vocabulaire total)} \\
N &= N_1 + N_2 \quad \text{(longueur totale)}
\end{align}
```

Où :
- **n₁** : Nombre d'opérateurs uniques
- **n₂** : Nombre d'opérandes uniques
- **N₁** : Nombre total d'opérateurs
- **N₂** : Nombre total d'opérandes

#### **Difficulté et Effort**
```math
\begin{align}
D &= \frac{n_1}{2} \times \frac{N_2}{n_2} \quad \text{(difficulté)} \\
E &= D \times N \quad \text{(effort)}
\end{align}
```

---

## 🔄 **GRADIENTS ET OPTIMISATION**

### 📈 **Gradient REINFORCE pour Proposition**

#### **Formule du Gradient**
```math
∇_θ J^{propose} = E_{z,τ} [∇_θ \log π_θ^{propose}(τ|z) × (r_e^{propose}(τ,π_θ) - b^{propose})]
```

#### **Baseline Adaptatif**
```math
b^{propose}(t+1) = αb^{propose}(t) + (1-α)r_e^{propose}(τ_t,π_θ)
```

Où **α = 0.99** est le facteur de lissage exponentiel.

### 📉 **Gradient REINFORCE pour Résolution**

#### **Formule du Gradient**
```math
∇_θ J^{solve} = E_{x,y} [∇_θ \log π_θ^{solve}(y|x) × (r_e^{solve}(y,y*) - b^{solve})]
```

#### **Baseline Adaptatif**
```math
b^{solve}(t+1) = αb^{solve}(t) + (1-α)r_e^{solve}(y_t,y*_t)
```

### 🔗 **Gradient Total**

#### **Combinaison Linéaire**
```math
∇_θ J = ∇_θ J^{propose} + λ × ∇_θ J^{solve}
```

#### **Mise à Jour des Paramètres**
```math
θ_{t+1} = θ_t + η × ∇_θ J
```

Où **η** est le taux d'apprentissage (typiquement η = 1e-6 à 5e-6).

---

## 🎯 **MÉTRIQUES D'ÉVALUATION**

### 📊 **Pass@k pour HumanEval**

#### **Formule Statistique**
```math
\text{Pass@k} = E_{Problems} \left[ 1 - \frac{\binom{n-c}{k}}{\binom{n}{k}} \right]
```

Où :
- **n** : Nombre de solutions générées par problème
- **c** : Nombre de solutions correctes
- **k** : Nombre de solutions considérées

#### **Estimation Non-Biaisée**
```math
\text{Pass@k} ≈ \frac{1}{|Problems|} \sum_{i=1}^{|Problems|} \max\left(0, 1 - \frac{n_i - c_i}{n_i} \times \frac{n_i - c_i - 1}{n_i - 1} \times ... \times \frac{n_i - c_i - k + 1}{n_i - k + 1}\right)
```

### 📈 **Accuracy sur GSM8K**

#### **Formule Simple**
```math
\text{Accuracy} = \frac{\text{Nombre de problèmes résolus correctement}}{\text{Nombre total de problèmes}}
```

#### **Avec Pondération par Difficulté**
```math
\text{Weighted Accuracy} = \frac{\sum_{i=1}^{n} w_i × \mathbb{I}(\text{correct}_i)}{\sum_{i=1}^{n} w_i}
```

Où **w_i** est le poids de difficulté du problème i.

---

## 🔬 **ANALYSE THÉORIQUE**

### 📊 **Convergence de l'Algorithme**

#### **Théorème de Convergence**
Sous certaines conditions de régularité, l'algorithme AZR converge vers un optimum local :

```math
\lim_{t→∞} E[||∇_θ J(θ_t)||^2] = 0
```

#### **Conditions Suffisantes**
1. **Gradient borné** : `||∇_θ J(θ)|| ≤ M` pour tout θ
2. **Taux d'apprentissage** : `∑ η_t = ∞` et `∑ η_t² < ∞`
3. **Variance finie** : `Var[∇_θ J(θ)] < ∞`

### ⚖️ **Équilibrage Optimal**

#### **Coefficient λ Optimal**
L'analyse empirique suggère que λ = 1.0 est optimal :

```math
λ^* = \arg\max_λ E[J(θ^*_λ)]
```

Où **θ*_λ** est l'optimum atteint avec le coefficient λ.

#### **Justification Intuitive**
- **λ < 1** : Sous-valorise la résolution → Proposeur crée tâches trop difficiles
- **λ > 1** : Sur-valorise la résolution → Proposeur crée tâches trop faciles
- **λ = 1** : Équilibre optimal entre exploration et exploitation

---

## 🎓 **RÉCAPITULATIF DU MODULE**

### ✅ **Formules Maîtrisées :**

1. **Fonction objectif** : J(θ) avec espérances imbriquées
2. **Récompense learnability** : 1 - r̄_solve pour difficulté optimale
3. **Récompense correctness** : Fonction indicatrice binaire
4. **Métriques qualité** : Diversité, complexité, Halstead
5. **Gradients REINFORCE** : Avec baselines adaptatifs

### 🎯 **Prochaine étape :**
[Module 3.2 - Gradients et Optimisation](./02_Gradients_Optimisation.md) pour approfondir les aspects algorithmiques.

---

## 🧠 **QUIZ DE VALIDATION**

### **Question 1** : Que vaut la récompense de learnability si r̄_solve = 0.6 ?
- A) 0.0
- B) 0.4
- C) 0.6
- D) 1.0

### **Question 2** : Quelle est la valeur optimale du coefficient λ ?
- A) 0.5
- B) 1.0
- C) 1.5
- D) 2.0

### **Question 3** : Comment calcule-t-on la complexité cyclomatique ?
- A) E - N + P
- B) E - N + 2P
- C) E + N - 2P
- D) 2E - N + P

**Réponses :** 1-B, 2-B, 3-B

---

**🎉 Excellent ! Vous maîtrisez maintenant les formules mathématiques fondamentales d'AZR !**
