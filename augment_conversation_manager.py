"""
🎛️ GESTIONNAIRE DE CONVERSATIONS AUGMENT
Interface pratique pour gérer la sauvegarde des conversations

Usage:
    python augment_conversation_manager.py --start
    python augment_conversation_manager.py --search "terme recherché"
    python augment_conversation_manager.py --export --format markdown
    python augment_conversation_manager.py --stats
"""

import argparse
import sys
from pathlib import Path
from conversation_logger import AugmentConversationLogger
import json

class ConversationManager:
    """
    🎛️ GESTIONNAIRE PRINCIPAL DES CONVERSATIONS
    
    Interface en ligne de commande pour toutes les opérations
    """
    
    def __init__(self):
        self.logger = AugmentConversationLogger()
        print("🗣️ Gestionnaire de conversations Augment initialisé")
    
    def start_logging(self):
        """Démarre l'enregistrement des conversations"""
        print("🚀 Enregistrement des conversations démarré")
        print(f"📁 Dossier: {self.logger.base_dir.absolute()}")
        print("\n💡 Pour enregistrer une conversation:")
        print("   - Utilisez log_user() et log_assistant() dans votre code")
        print("   - Ou importez ce module dans vos scripts")
        
        # Exemple d'utilisation
        print("\n📝 Exemple d'utilisation:")
        print("```python")
        print("from augment_conversation_manager import get_global_logger")
        print("")
        print("logger = get_global_logger()")
        print("logger.log_user_message('Ma question à Augment')")
        print("logger.log_assistant_message('Réponse d\\'Augment')")
        print("```")
    
    def search_conversations(self, query: str, date_start: str = None, date_end: str = None):
        """Recherche dans les conversations"""
        print(f"🔍 Recherche: '{query}'")
        
        date_range = None
        if date_start and date_end:
            date_range = (date_start, date_end)
            print(f"📅 Période: {date_start} à {date_end}")
        
        results = self.logger.search_conversations(query, date_range)
        
        if not results:
            print("❌ Aucun résultat trouvé")
            return
        
        print(f"✅ {len(results)} résultats trouvés:\n")
        
        for i, result in enumerate(results[:10], 1):  # Limiter à 10 résultats
            msg = result['message']
            print(f"{i}. [{result['date']}] {msg['role'].upper()}")
            print(f"   Session: {result['session_id'][:8]}...")
            print(f"   Workspace: {result['workspace']}")
            
            # Afficher un extrait du message
            content = msg['content']
            if len(content) > 200:
                content = content[:200] + "..."
            print(f"   Contenu: {content}")
            print("-" * 50)
        
        if len(results) > 10:
            print(f"... et {len(results) - 10} autres résultats")
    
    def export_conversations(self, format_type: str = 'json', output_file: str = None, 
                           date_start: str = None, date_end: str = None):
        """Exporte les conversations"""
        if not output_file:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"conversations_export_{timestamp}.{format_type}"
        
        date_range = None
        if date_start and date_end:
            date_range = (date_start, date_end)
        
        print(f"📤 Export en cours...")
        print(f"   Format: {format_type}")
        print(f"   Fichier: {output_file}")
        
        if date_range:
            print(f"   Période: {date_start} à {date_end}")
        
        self.logger.export_conversations(output_file, date_range, format_type)
        
        export_path = self.logger.base_dir / 'exports' / output_file
        print(f"✅ Export terminé: {export_path}")
    
    def show_statistics(self):
        """Affiche les statistiques des conversations"""
        print("📊 Calcul des statistiques...")
        stats = self.logger.get_statistics()
        
        if not stats:
            print("❌ Aucune donnée trouvée")
            return
        
        print("\n" + "="*50)
        print("📊 STATISTIQUES DES CONVERSATIONS AUGMENT")
        print("="*50)
        
        print(f"📈 Sessions totales: {stats['total_sessions']}")
        print(f"💬 Messages totaux: {stats['total_messages']}")
        print(f"👤 Messages utilisateur: {stats['user_messages']}")
        print(f"🤖 Messages assistant: {stats['assistant_messages']}")
        
        if stats['date_range']['first']:
            print(f"📅 Première conversation: {stats['date_range']['first']}")
            print(f"📅 Dernière conversation: {stats['date_range']['last']}")
        
        print(f"📁 Workspaces utilisés: {len(stats['workspaces'])}")
        for workspace in stats['workspaces'][:5]:  # Afficher les 5 premiers
            print(f"   - {workspace}")
        
        if len(stats['workspaces']) > 5:
            print(f"   ... et {len(stats['workspaces']) - 5} autres")
        
        # Répartition quotidienne (derniers 7 jours)
        print("\n📈 Activité récente (derniers jours):")
        daily_items = list(stats['daily_breakdown'].items())
        daily_items.sort(reverse=True)  # Plus récent en premier
        
        for date, day_stats in daily_items[:7]:
            print(f"   {date}: {day_stats['sessions']} sessions, {day_stats['messages']} messages")
    
    def interactive_mode(self):
        """Mode interactif pour tester le logger"""
        print("🎮 Mode interactif - Testez le logger")
        print("Tapez 'quit' pour quitter\n")
        
        while True:
            try:
                user_input = input("👤 Vous: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not user_input:
                    continue
                
                # Enregistrer le message utilisateur
                self.logger.log_user_message(user_input)
                
                # Simuler une réponse assistant
                assistant_response = f"Message reçu et enregistré: '{user_input[:50]}...'"
                self.logger.log_assistant_message(assistant_response)
                
                print(f"🤖 Assistant: {assistant_response}")
                print(f"💾 Messages enregistrés dans la session {self.logger.session_id[:8]}...\n")
                
            except KeyboardInterrupt:
                break
        
        print("\n🏁 Session terminée")
        self.logger.end_session()

# Instance globale pour utilisation facile
_global_logger = None

def get_global_logger() -> AugmentConversationLogger:
    """Obtient l'instance globale du logger"""
    global _global_logger
    if _global_logger is None:
        _global_logger = AugmentConversationLogger()
    return _global_logger

def log_user(message: str, metadata: dict = None):
    """Fonction rapide pour enregistrer un message utilisateur"""
    logger = get_global_logger()
    logger.log_user_message(message, metadata)

def log_assistant(message: str, metadata: dict = None):
    """Fonction rapide pour enregistrer un message assistant"""
    logger = get_global_logger()
    logger.log_assistant_message(message, metadata)

def main():
    """Point d'entrée principal"""
    parser = argparse.ArgumentParser(description="Gestionnaire de conversations Augment")
    
    parser.add_argument('--start', action='store_true', help='Démarrer l\'enregistrement')
    parser.add_argument('--search', type=str, help='Rechercher dans les conversations')
    parser.add_argument('--export', action='store_true', help='Exporter les conversations')
    parser.add_argument('--stats', action='store_true', help='Afficher les statistiques')
    parser.add_argument('--interactive', action='store_true', help='Mode interactif')
    
    parser.add_argument('--format', choices=['json', 'txt', 'markdown'], default='json',
                       help='Format d\'export')
    parser.add_argument('--output', type=str, help='Fichier de sortie')
    parser.add_argument('--date-start', type=str, help='Date de début (YYYY-MM-DD)')
    parser.add_argument('--date-end', type=str, help='Date de fin (YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    manager = ConversationManager()
    
    if args.start:
        manager.start_logging()
    
    elif args.search:
        manager.search_conversations(args.search, args.date_start, args.date_end)
    
    elif args.export:
        manager.export_conversations(args.format, args.output, args.date_start, args.date_end)
    
    elif args.stats:
        manager.show_statistics()
    
    elif args.interactive:
        manager.interactive_mode()

if __name__ == "__main__":
    main()
