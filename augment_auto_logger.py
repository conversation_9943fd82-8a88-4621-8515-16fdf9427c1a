"""
🔄 AUTO-LOGGER POUR CONVERSATIONS AUGMENT
Intercepte automatiquement les conversations et les sauvegarde

Ce script peut être intégré pour capturer automatiquement
les interactions avec Augment Agent.
"""

import sys
import os
import time
import threading
from pathlib import Path
from datetime import datetime
from augment_conversation_manager import get_global_logger

class AugmentAutoLogger:
    """
    🔄 LOGGER AUTOMATIQUE POUR AUGMENT
    
    Intercepte et sauvegarde automatiquement les conversations
    """
    
    def __init__(self, enable_auto_capture: bool = True):
        self.logger = get_global_logger()
        self.enable_auto_capture = enable_auto_capture
        self.last_user_input = ""
        self.conversation_active = False
        
        print("🔄 Auto-logger Augment initialisé")
        
        if enable_auto_capture:
            self._setup_auto_capture()
    
    def _setup_auto_capture(self):
        """Configure la capture automatique"""
        print("⚙️ Configuration de la capture automatique...")
        
        # Créer un fichier de monitoring
        self.monitor_file = Path("augment_conversation_monitor.txt")
        
        # Démarrer le thread de monitoring
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_conversations, daemon=True)
        self.monitor_thread.start()
        
        print("✅ Capture automatique activée")
        print(f"📝 Fichier de monitoring: {self.monitor_file}")
    
    def _monitor_conversations(self):
        """Thread de monitoring des conversations"""
        last_modified = 0
        
        while self.monitoring:
            try:
                if self.monitor_file.exists():
                    current_modified = self.monitor_file.stat().st_mtime
                    
                    if current_modified > last_modified:
                        last_modified = current_modified
                        self._process_monitor_file()
                
                time.sleep(1)  # Vérifier chaque seconde
                
            except Exception as e:
                print(f"❌ Erreur monitoring: {e}")
                time.sleep(5)
    
    def _process_monitor_file(self):
        """Traite le fichier de monitoring"""
        try:
            with open(self.monitor_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if not content:
                return
            
            # Parser le contenu
            lines = content.split('\n')
            for line in lines:
                if line.startswith('USER:'):
                    message = line[5:].strip()
                    if message and message != self.last_user_input:
                        self.log_user_input(message)
                        self.last_user_input = message
                
                elif line.startswith('ASSISTANT:'):
                    message = line[10:].strip()
                    if message:
                        self.log_assistant_response(message)
            
            # Nettoyer le fichier après traitement
            self.monitor_file.write_text("", encoding='utf-8')
            
        except Exception as e:
            print(f"❌ Erreur traitement fichier: {e}")
    
    def log_user_input(self, message: str, metadata: dict = None):
        """
        📝 ENREGISTRE UN MESSAGE UTILISATEUR
        
        Args:
            message: Message de l'utilisateur
            metadata: Métadonnées optionnelles
        """
        if not self.enable_auto_capture:
            return
        
        # Ajouter métadonnées automatiques
        auto_metadata = {
            'timestamp': datetime.now().isoformat(),
            'source': 'auto_logger',
            'workspace': os.getcwd(),
            'character_count': len(message)
        }
        
        if metadata:
            auto_metadata.update(metadata)
        
        self.logger.log_user_message(message, auto_metadata)
        print(f"📝 Message utilisateur enregistré ({len(message)} caractères)")
        
        self.conversation_active = True
    
    def log_assistant_response(self, message: str, metadata: dict = None):
        """
        🤖 ENREGISTRE UNE RÉPONSE ASSISTANT
        
        Args:
            message: Réponse de l'assistant
            metadata: Métadonnées optionnelles
        """
        if not self.enable_auto_capture:
            return
        
        # Ajouter métadonnées automatiques
        auto_metadata = {
            'timestamp': datetime.now().isoformat(),
            'source': 'auto_logger',
            'workspace': os.getcwd(),
            'character_count': len(message),
            'response_time': time.time()  # Pour calculer le temps de réponse
        }
        
        if metadata:
            auto_metadata.update(metadata)
        
        self.logger.log_assistant_message(message, auto_metadata)
        print(f"🤖 Réponse assistant enregistrée ({len(message)} caractères)")
    
    def manual_log_conversation(self, user_message: str, assistant_response: str):
        """
        ✋ ENREGISTREMENT MANUEL D'UNE CONVERSATION COMPLÈTE
        
        Args:
            user_message: Message de l'utilisateur
            assistant_response: Réponse de l'assistant
        """
        print("✋ Enregistrement manuel d'une conversation...")
        
        self.log_user_input(user_message, {'source': 'manual_entry'})
        self.log_assistant_response(assistant_response, {'source': 'manual_entry'})
        
        print("✅ Conversation enregistrée manuellement")
    
    def create_monitor_entry(self, role: str, message: str):
        """
        📝 CRÉE UNE ENTRÉE DANS LE FICHIER DE MONITORING
        
        Utilisez cette fonction pour alimenter le monitoring automatique
        """
        try:
            with open(self.monitor_file, 'a', encoding='utf-8') as f:
                f.write(f"{role.upper()}: {message}\n")
        except Exception as e:
            print(f"❌ Erreur écriture monitoring: {e}")
    
    def end_conversation(self):
        """Termine la conversation actuelle"""
        if self.conversation_active:
            self.logger.end_session()
            self.conversation_active = False
            print("🏁 Conversation terminée et sauvegardée")
    
    def stop_monitoring(self):
        """Arrête le monitoring automatique"""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=2)
        print("⏹️ Monitoring arrêté")
    
    def get_session_info(self):
        """Retourne les informations de la session actuelle"""
        if self.logger.current_session:
            return {
                'session_id': self.logger.session_id,
                'start_time': self.logger.current_session.start_time,
                'message_count': self.logger.current_session.total_messages,
                'workspace': self.logger.current_session.workspace_path
            }
        return None

# Instance globale
_auto_logger = None

def get_auto_logger() -> AugmentAutoLogger:
    """Obtient l'instance globale de l'auto-logger"""
    global _auto_logger
    if _auto_logger is None:
        _auto_logger = AugmentAutoLogger()
    return _auto_logger

def quick_log_user(message: str):
    """Fonction rapide pour enregistrer un message utilisateur"""
    logger = get_auto_logger()
    logger.log_user_input(message)

def quick_log_assistant(message: str):
    """Fonction rapide pour enregistrer une réponse assistant"""
    logger = get_auto_logger()
    logger.log_assistant_response(message)

def quick_log_conversation(user_msg: str, assistant_msg: str):
    """Fonction rapide pour enregistrer une conversation complète"""
    logger = get_auto_logger()
    logger.manual_log_conversation(user_msg, assistant_msg)

# Fonctions d'intégration pour VSCode/Augment
def augment_integration_hook():
    """
    🔗 HOOK D'INTÉGRATION POUR AUGMENT
    
    Cette fonction peut être appelée par des extensions ou scripts
    pour s'intégrer automatiquement avec le système de logging
    """
    print("🔗 Hook d'intégration Augment activé")
    
    # Créer le fichier de configuration d'intégration
    integration_config = {
        'auto_logging_enabled': True,
        'monitor_file': str(Path("augment_conversation_monitor.txt").absolute()),
        'log_directory': str(Path("augment_conversations").absolute()),
        'integration_version': '1.0.0',
        'created_at': datetime.now().isoformat()
    }
    
    config_file = Path("augment_integration_config.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        import json
        json.dump(integration_config, f, indent=2, ensure_ascii=False)
    
    print(f"⚙️ Configuration d'intégration créée: {config_file}")
    
    # Démarrer l'auto-logger
    auto_logger = get_auto_logger()
    
    return {
        'status': 'active',
        'config_file': str(config_file),
        'monitor_file': integration_config['monitor_file'],
        'session_id': auto_logger.logger.session_id
    }

if __name__ == "__main__":
    # Test du système
    print("🧪 Test de l'auto-logger Augment")
    
    auto_logger = get_auto_logger()
    
    # Test manuel
    auto_logger.manual_log_conversation(
        "Bonjour Augment, peux-tu m'aider avec mon code ?",
        "Bien sûr ! Je suis là pour vous aider. Quel est votre problème ?"
    )
    
    # Afficher les infos de session
    session_info = auto_logger.get_session_info()
    if session_info:
        print(f"📊 Session: {session_info['session_id'][:8]}...")
        print(f"📊 Messages: {session_info['message_count']}")
    
    # Test du hook d'intégration
    integration_result = augment_integration_hook()
    print(f"🔗 Intégration: {integration_result['status']}")
    
    print("✅ Test terminé")
