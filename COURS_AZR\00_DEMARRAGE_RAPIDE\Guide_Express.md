# ⚡ GUIDE DE DÉMARRAGE EXPRESS - AZR

## 🎯 **OBJECTIF**

Comprendre et implémenter AZR en **2 heures** avec les concepts essentiels.

---

## 🚀 **ÉTAPE 1 - COMPRÉHENSION (30 min)**

### 🧠 **Qu'est-ce qu'AZR ?**

**AZR = Absolute Zero Reasoner** : Une IA qui s'auto-améliore **sans données humaines**.

#### **Principe Révolutionnaire**
```
IA Traditionnelle: Donn<PERSON> → Modèle → Performance Limitée
AZR: Modèle → Auto-Génération → Performance Illimitée
```

#### **Les 2 Rôles Unifiés**
1. **🎯 Proposeur** : Crée des exercices d'apprentissage
2. **🔧 Résolveur** : Résout les exercices créés

#### **Cycle d'Auto-Amélioration**
```
Proposer <PERSON><PERSON><PERSON> → Résoudre → Évaluer → Améliorer → Répéter
```

### 📐 **Formule Magique**

```math
J(θ) = Récompense_Proposition + λ × Récompense_Résolution
```

**Où :**
- **Récompense_Proposition** : 1 - taux_réussite (difficulté optimale)
- **Récompense_Résolution** : 1 si correct, 0 sinon
- **λ = 1.0** : Équilibrage parfait

---

## ⚙️ **ÉTAPE 2 - INSTALLATION EXPRESS (20 min)**

### 🐍 **Setup Minimal**

```bash
# 1. Environnement Python
python -m venv azr_env
source azr_env/bin/activate  # Linux/Mac
# azr_env\Scripts\activate  # Windows

# 2. Dépendances essentielles
pip install torch transformers numpy

# 3. Test rapide
python -c "import torch; print('✅ PyTorch OK')"
```

### 📁 **Structure de Projet**

```
azr_minimal/
├── azr_model.py      # Modèle principal
├── environment.py    # Environnement d'exécution
├── trainer.py        # Entraînement
└── demo.py          # Démonstration
```

---

## 💻 **ÉTAPE 3 - IMPLÉMENTATION MINIMALE (60 min)**

### 🧠 **azr_model.py - Cœur du Système**

```python
import torch
import torch.nn as nn
from transformers import AutoTokenizer, AutoModelForCausalLM
import random

class AZRModel:
    def __init__(self, model_name="microsoft/DialoGPT-small"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(model_name)
        self.tokenizer.pad_token = self.tokenizer.eos_token
        
    def propose_task(self, context=""):
        """Propose une nouvelle tâche de programmation"""
        
        prompt = f"""
        Crée un exercice de programmation Python simple.
        Format: Programme|Input|Output
        
        Exemple: x = int(input())\nprint(x * 2)|5|10
        
        Contexte: {context}
        
        Nouveau exercice:
        """
        
        inputs = self.tokenizer.encode(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.model.generate(
                inputs,
                max_length=inputs.shape[1] + 100,
                temperature=0.8,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        task_part = response.split("Nouveau exercice:")[-1].strip()
        
        return self._parse_task(task_part)
    
    def solve_task(self, question):
        """Résout une tâche donnée"""
        
        prompt = f"""
        Résous ce problème de programmation Python:
        
        {question}
        
        Solution:
        """
        
        inputs = self.tokenizer.encode(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.model.generate(
                inputs,
                max_length=inputs.shape[1] + 150,
                temperature=0.6,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        solution = response.split("Solution:")[-1].strip()
        
        return solution
    
    def _parse_task(self, task_text):
        """Parse une tâche au format Programme|Input|Output"""
        try:
            parts = task_text.split("|")
            if len(parts) >= 3:
                return {
                    "program": parts[0].strip(),
                    "input": parts[1].strip(),
                    "output": parts[2].strip()
                }
        except:
            pass
        
        # Fallback: tâche simple
        return {
            "program": "x = int(input())\nprint(x + 1)",
            "input": "5",
            "output": "6"
        }
```

### 🌍 **environment.py - Validation**

```python
import subprocess
import tempfile
import os

class PythonEnvironment:
    def __init__(self, timeout=5):
        self.timeout = timeout
    
    def execute_code(self, code, input_data=""):
        """Exécute du code Python de manière sécurisée"""
        
        try:
            # Création d'un fichier temporaire
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            # Exécution avec subprocess
            process = subprocess.Popen(
                ['python', temp_file],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            stdout, stderr = process.communicate(
                input=input_data,
                timeout=self.timeout
            )
            
            # Nettoyage
            os.unlink(temp_file)
            
            return {
                'success': process.returncode == 0,
                'output': stdout.strip(),
                'error': stderr.strip()
            }
            
        except subprocess.TimeoutExpired:
            return {'success': False, 'output': '', 'error': 'Timeout'}
        except Exception as e:
            return {'success': False, 'output': '', 'error': str(e)}
    
    def validate_task(self, program, input_data, expected_output):
        """Valide qu'une tâche est cohérente"""
        
        result = self.execute_code(program, input_data)
        
        if not result['success']:
            return False
        
        return result['output'] == expected_output
    
    def validate_solution(self, solution, expected_output):
        """Valide une solution"""
        
        result = self.execute_code(solution)
        
        if not result['success']:
            return False
        
        return result['output'] == expected_output
```

### 🎓 **trainer.py - Entraînement Simplifié**

```python
import random
from collections import deque

class AZRTrainer:
    def __init__(self, model, environment):
        self.model = model
        self.env = environment
        self.task_buffer = deque(maxlen=100)
        self.baseline_propose = 0.0
        self.baseline_solve = 0.0
        
    def training_step(self):
        """Une étape d'entraînement AZR"""
        
        metrics = {
            'proposed_tasks': 0,
            'valid_tasks': 0,
            'solved_tasks': 0,
            'avg_propose_reward': 0.0,
            'avg_solve_reward': 0.0
        }
        
        # Phase 1: Proposition
        context = self._get_context()
        proposed_tasks = []
        propose_rewards = []
        
        for _ in range(5):  # 5 tâches par batch
            task = self.model.propose_task(context)
            metrics['proposed_tasks'] += 1
            
            # Validation de la tâche
            if self.env.validate_task(task['program'], task['input'], task['output']):
                proposed_tasks.append(task)
                metrics['valid_tasks'] += 1
                
                # Calcul de la récompense de learnability
                difficulty = self._calculate_difficulty(task)
                reward = self._learnability_reward(difficulty)
                propose_rewards.append(reward)
        
        # Phase 2: Résolution
        solve_rewards = []
        
        for task in proposed_tasks:
            question = f"Programme: {task['program']}\nInput: {task['input']}\nQuel est l'output?"
            solution = self.model.solve_task(question)
            
            # Validation de la solution
            is_correct = self.env.validate_solution(solution, task['output'])
            reward = 1.0 if is_correct else 0.0
            solve_rewards.append(reward)
            
            if is_correct:
                metrics['solved_tasks'] += 1
                self.task_buffer.append(task)
        
        # Calcul des métriques
        if propose_rewards:
            metrics['avg_propose_reward'] = sum(propose_rewards) / len(propose_rewards)
        if solve_rewards:
            metrics['avg_solve_reward'] = sum(solve_rewards) / len(solve_rewards)
        
        # Mise à jour des baselines (simplifié)
        if propose_rewards:
            self.baseline_propose = 0.9 * self.baseline_propose + 0.1 * metrics['avg_propose_reward']
        if solve_rewards:
            self.baseline_solve = 0.9 * self.baseline_solve + 0.1 * metrics['avg_solve_reward']
        
        return metrics
    
    def _get_context(self):
        """Récupère le contexte des tâches passées"""
        if len(self.task_buffer) < 3:
            return "Crée des exercices simples de programmation Python."
        
        recent_tasks = list(self.task_buffer)[-3:]
        context = "Exemples récents:\n"
        for task in recent_tasks:
            context += f"- {task['program']} | {task['input']} | {task['output']}\n"
        context += "Crée quelque chose de différent et créatif."
        
        return context
    
    def _calculate_difficulty(self, task):
        """Calcule la difficulté d'une tâche (simplifié)"""
        # Simulation: teste la tâche 3 fois
        successes = 0
        for _ in range(3):
            question = f"Programme: {task['program']}\nInput: {task['input']}\nQuel est l'output?"
            solution = self.model.solve_task(question)
            if self.env.validate_solution(solution, task['output']):
                successes += 1
        
        return successes / 3
    
    def _learnability_reward(self, difficulty):
        """Calcule la récompense de learnability"""
        if difficulty == 0.0 or difficulty == 1.0:
            return 0.0  # Trop facile ou impossible
        else:
            return 1.0 - difficulty  # Difficulté optimale
```

### 🎮 **demo.py - Démonstration**

```python
from azr_model import AZRModel
from environment import PythonEnvironment
from trainer import AZRTrainer

def main():
    print("🚀 DÉMONSTRATION AZR EXPRESS")
    print("=" * 40)
    
    # Initialisation
    print("📦 Chargement du modèle...")
    model = AZRModel()
    
    print("🌍 Initialisation de l'environnement...")
    env = PythonEnvironment()
    
    print("🎓 Création du trainer...")
    trainer = AZRTrainer(model, env)
    
    # Démonstration
    print("\n🎯 DÉMONSTRATION DES CAPACITÉS")
    print("-" * 40)
    
    # Test de proposition
    print("\n1. 🎨 Proposition de tâche:")
    task = model.propose_task("Crée un exercice simple")
    print(f"   Programme: {task['program']}")
    print(f"   Input: {task['input']}")
    print(f"   Output attendu: {task['output']}")
    
    # Validation de la tâche
    is_valid = env.validate_task(task['program'], task['input'], task['output'])
    print(f"   ✅ Tâche valide: {is_valid}")
    
    # Test de résolution
    print("\n2. 🔧 Résolution de tâche:")
    question = f"Programme: {task['program']}\nInput: {task['input']}\nQuel est l'output?"
    solution = model.solve_task(question)
    print(f"   Solution proposée: {solution}")
    
    # Validation de la solution
    is_correct = env.validate_solution(solution, task['output'])
    print(f"   ✅ Solution correcte: {is_correct}")
    
    # Entraînement express
    print("\n3. 🎓 Entraînement express (5 itérations):")
    for i in range(5):
        metrics = trainer.training_step()
        print(f"   Itération {i+1}: {metrics['valid_tasks']}/{metrics['proposed_tasks']} tâches valides, "
              f"{metrics['solved_tasks']} résolues")
    
    print("\n🎉 Démonstration terminée !")
    print("💡 Vous avez vu AZR en action : proposition, résolution, et auto-amélioration !")

if __name__ == "__main__":
    main()
```

---

## 🏃‍♂️ **ÉTAPE 4 - EXÉCUTION (10 min)**

### 🚀 **Lancement de la Démo**

```bash
# Dans le dossier azr_minimal/
python demo.py

# Vous devriez voir :
# 🚀 DÉMONSTRATION AZR EXPRESS
# ================================
# 📦 Chargement du modèle...
# 🌍 Initialisation de l'environnement...
# ...
# 🎉 Démonstration terminée !
```

### 📊 **Résultats Attendus**

- **Proposition** : Génération de tâches Python simples
- **Validation** : Vérification automatique des tâches
- **Résolution** : Tentatives de résolution par le modèle
- **Amélioration** : Métriques d'apprentissage progressif

---

## 🎯 **CONCEPTS CLÉS MAÎTRISÉS**

### ✅ **En 2 heures, vous avez :**

1. **Compris** le paradigme révolutionnaire d'AZR
2. **Installé** un environnement fonctionnel
3. **Implémenté** un système AZR minimal mais complet
4. **Testé** la proposition, résolution et auto-amélioration
5. **Observé** l'apprentissage sans données externes

### 🚀 **Prochaines Étapes**

#### **Pour Approfondir :**
- [Module 2.1 - Architecture Générale](../02_ARCHITECTURE/01_Architecture_Generale.md)
- [Module 3.1 - Formules Fondamentales](../03_MATHEMATIQUES/01_Formules_Fondamentales.md)
- [Module 4.1 - Configuration Setup](../04_IMPLEMENTATION/01_Configuration_Setup.md)

#### **Pour Optimiser :**
- Modèles plus grands (7B+ paramètres)
- Sandbox Docker sécurisé
- Métriques avancées de qualité
- Monitoring avec Weights & Biases

#### **Pour Innover :**
- Nouveaux types de raisonnement
- Domaines d'application étendus
- Techniques d'optimisation avancées

---

## 🎉 **FÉLICITATIONS !**

**Vous venez de maîtriser les fondamentaux d'AZR en 2 heures !**

Vous avez maintenant :
- ✅ Une compréhension solide du paradigme
- ✅ Un système AZR fonctionnel
- ✅ Les bases pour des implémentations avancées
- ✅ La capacité d'expérimenter et d'innover

**Bienvenue dans l'univers fascinant de l'IA auto-améliorante !** 🚀
