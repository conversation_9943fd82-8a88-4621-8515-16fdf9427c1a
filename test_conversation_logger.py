"""
🧪 TEST DU SYSTÈME DE SAUVEGARDE DES CONVERSATIONS AUGMENT
Script de démonstration et de validation du système
"""

import time
import json
from datetime import datetime, timedelta
from pathlib import Path

from conversation_logger import AugmentConversationLogger
from augment_conversation_manager import ConversationManager, get_global_logger
from augment_auto_logger import get_auto_logger, quick_log_conversation

def test_basic_logging():
    """Test des fonctionnalités de base du logger"""
    print("🧪 Test 1: Fonctionnalités de base")
    print("-" * 40)
    
    logger = AugmentConversationLogger("test_conversations")
    
    # Test d'enregistrement de messages
    logger.log_user_message("Bonjour Augment, comment ça va ?")
    logger.log_assistant_message("Bonjour ! Je vais très bien, merci. Comment puis-je vous aider aujourd'hui ?")
    
    logger.log_user_message("Peux-tu m'expliquer le système AZR ?")
    logger.log_assistant_message("""Le système AZR (Actor-Critic with Rollouts) est une architecture d'apprentissage par renforcement qui combine :

1. **Rôle Proposeur** : Génère des hypothèses et solutions
2. **Rôle Résolveur** : Évalue et valide les propositions
3. **Rollouts** : Simulations pour améliorer les prédictions

C'est particulièrement efficace pour les problèmes de prédiction séquentielle comme le baccarat.""")
    
    logger.log_user_message("Merci pour l'explication !")
    logger.log_assistant_message("De rien ! N'hésitez pas si vous avez d'autres questions.")
    
    # Terminer la session
    logger.end_session()
    
    print(f"✅ Session créée avec {logger.message_count} messages")
    print(f"📁 Dossier: {logger.base_dir}")
    
    return logger

def test_search_functionality():
    """Test de la fonctionnalité de recherche"""
    print("\n🧪 Test 2: Recherche dans les conversations")
    print("-" * 40)
    
    logger = get_global_logger()
    
    # Ajouter quelques conversations de test
    conversations = [
        ("Comment fonctionne le machine learning ?", "Le machine learning utilise des algorithmes pour apprendre des patterns dans les données..."),
        ("Qu'est-ce que Python ?", "Python est un langage de programmation polyvalent et facile à apprendre..."),
        ("Explique-moi les réseaux de neurones", "Les réseaux de neurones sont inspirés du cerveau humain..."),
        ("Comment optimiser mon code Python ?", "Voici quelques techniques d'optimisation pour Python...")
    ]
    
    for user_msg, assistant_msg in conversations:
        logger.log_user_message(user_msg)
        logger.log_assistant_message(assistant_msg)
    
    # Test de recherche
    search_terms = ["Python", "machine learning", "optimisation"]
    
    for term in search_terms:
        results = logger.search_conversations(term)
        print(f"🔍 Recherche '{term}': {len(results)} résultats")
        
        for result in results[:2]:  # Afficher les 2 premiers
            msg = result['message']
            content_preview = msg['content'][:100] + "..." if len(msg['content']) > 100 else msg['content']
            print(f"   - {msg['role']}: {content_preview}")
    
    return len(search_terms)

def test_export_functionality():
    """Test des fonctionnalités d'export"""
    print("\n🧪 Test 3: Export des conversations")
    print("-" * 40)
    
    logger = get_global_logger()
    
    # Test export JSON
    logger.export_conversations("test_export.json", format_type='json')
    
    # Test export Markdown
    logger.export_conversations("test_export.md", format_type='markdown')
    
    # Test export TXT
    logger.export_conversations("test_export.txt", format_type='txt')
    
    # Vérifier que les fichiers ont été créés
    export_dir = logger.base_dir / 'exports'
    exports = list(export_dir.glob('test_export.*'))
    
    print(f"✅ {len(exports)} fichiers d'export créés:")
    for export_file in exports:
        size = export_file.stat().st_size
        print(f"   - {export_file.name} ({size} bytes)")
    
    return len(exports)

def test_statistics():
    """Test des statistiques"""
    print("\n🧪 Test 4: Statistiques")
    print("-" * 40)
    
    logger = get_global_logger()
    stats = logger.get_statistics()
    
    print("📊 Statistiques générées:")
    print(f"   - Sessions: {stats.get('total_sessions', 0)}")
    print(f"   - Messages: {stats.get('total_messages', 0)}")
    print(f"   - Messages utilisateur: {stats.get('user_messages', 0)}")
    print(f"   - Messages assistant: {stats.get('assistant_messages', 0)}")
    print(f"   - Workspaces: {len(stats.get('workspaces', []))}")
    
    return stats

def test_auto_logger():
    """Test de l'auto-logger"""
    print("\n🧪 Test 5: Auto-logger")
    print("-" * 40)
    
    auto_logger = get_auto_logger()
    
    # Test d'enregistrement rapide
    quick_log_conversation(
        "Test de l'auto-logger",
        "L'auto-logger fonctionne correctement !"
    )
    
    # Test des informations de session
    session_info = auto_logger.get_session_info()
    if session_info:
        print(f"📊 Session auto-logger: {session_info['session_id'][:8]}...")
        print(f"📊 Messages: {session_info['message_count']}")
    
    return session_info

def test_manager_interface():
    """Test de l'interface manager"""
    print("\n🧪 Test 6: Interface Manager")
    print("-" * 40)
    
    manager = ConversationManager()
    
    # Simuler quelques opérations
    print("🔍 Test de recherche via manager...")
    manager.search_conversations("test", None, None)
    
    print("📊 Test des statistiques via manager...")
    manager.show_statistics()
    
    return True

def create_sample_conversations():
    """Crée des conversations d'exemple pour les tests"""
    print("\n📝 Création de conversations d'exemple...")
    
    logger = get_global_logger()
    
    # Conversations d'exemple variées
    sample_conversations = [
        {
            "user": "Comment installer Python sur Windows ?",
            "assistant": "Pour installer Python sur Windows :\n1. Allez sur python.org\n2. Téléchargez la dernière version\n3. Exécutez l'installateur\n4. Cochez 'Add to PATH'\n5. Suivez les instructions"
        },
        {
            "user": "Qu'est-ce que Git ?",
            "assistant": "Git est un système de contrôle de version distribué qui permet de :\n- Suivre les modifications du code\n- Collaborer avec d'autres développeurs\n- Gérer les branches et fusions\n- Conserver un historique complet"
        },
        {
            "user": "Comment déboguer du code Python ?",
            "assistant": "Plusieurs techniques de débogage Python :\n1. print() pour afficher des valeurs\n2. pdb pour le débogage interactif\n3. IDE avec débogueur intégré\n4. logging pour tracer l'exécution\n5. assert pour vérifier les conditions"
        },
        {
            "user": "Explique les bases de données",
            "assistant": "Les bases de données stockent et organisent les données :\n- **Relationnelles** (SQL) : MySQL, PostgreSQL\n- **NoSQL** : MongoDB, Redis\n- **Concepts** : tables, requêtes, index\n- **CRUD** : Create, Read, Update, Delete"
        }
    ]
    
    for conv in sample_conversations:
        logger.log_user_message(conv["user"])
        logger.log_assistant_message(conv["assistant"])
        time.sleep(0.1)  # Petit délai pour différencier les timestamps
    
    print(f"✅ {len(sample_conversations)} conversations d'exemple créées")
    return len(sample_conversations)

def run_comprehensive_test():
    """Lance tous les tests de manière séquentielle"""
    print("🚀 DÉMARRAGE DES TESTS COMPLETS")
    print("=" * 50)
    
    start_time = time.time()
    results = {}
    
    try:
        # Test 1: Fonctionnalités de base
        logger = test_basic_logging()
        results['basic_logging'] = True
        
        # Créer des conversations d'exemple
        sample_count = create_sample_conversations()
        results['sample_conversations'] = sample_count
        
        # Test 2: Recherche
        search_count = test_search_functionality()
        results['search'] = search_count
        
        # Test 3: Export
        export_count = test_export_functionality()
        results['export'] = export_count
        
        # Test 4: Statistiques
        stats = test_statistics()
        results['statistics'] = stats
        
        # Test 5: Auto-logger
        session_info = test_auto_logger()
        results['auto_logger'] = session_info is not None
        
        # Test 6: Manager
        manager_ok = test_manager_interface()
        results['manager'] = manager_ok
        
    except Exception as e:
        print(f"❌ Erreur pendant les tests: {e}")
        results['error'] = str(e)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Résumé des résultats
    print("\n" + "=" * 50)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    print(f"⏱️ Durée totale: {duration:.2f} secondes")
    print(f"✅ Tests réussis: {sum(1 for v in results.values() if v)}")
    print(f"❌ Tests échoués: {sum(1 for v in results.values() if not v)}")
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}: {result}")
    
    # Informations sur les fichiers créés
    print("\n📁 Fichiers créés:")
    base_dir = Path("augment_conversations")
    if base_dir.exists():
        for subdir in base_dir.iterdir():
            if subdir.is_dir():
                file_count = len(list(subdir.glob("*")))
                print(f"   - {subdir.name}/: {file_count} fichiers")
    
    print("\n🎉 Tests terminés !")
    return results

if __name__ == "__main__":
    # Lancer les tests complets
    results = run_comprehensive_test()
    
    # Sauvegarder les résultats
    results_file = Path("test_results.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'results': results,
            'test_version': '1.0.0'
        }, f, indent=2, ensure_ascii=False)
    
    print(f"📊 Résultats sauvegardés: {results_file}")
