"""
🚀 DÉMARRAGE RAPIDE - SAUVEGARDE DES CONVERSATIONS AUGMENT
Script simple pour commencer à utiliser le système de sauvegarde
"""

import os
import sys
from pathlib import Path
from datetime import datetime

def setup_conversation_logging():
    """Configure et démarre le système de sauvegarde"""
    print("🚀 Configuration du système de sauvegarde des conversations Augment")
    print("=" * 60)
    
    # Vérifier que les fichiers nécessaires existent
    required_files = [
        'conversation_logger.py',
        'augment_conversation_manager.py',
        'augment_auto_logger.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ Fichiers manquants:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n💡 Assurez-vous que tous les fichiers sont présents dans le dossier.")
        return False
    
    print("✅ Tous les fichiers requis sont présents")
    
    # Importer et initialiser le système
    try:
        from augment_auto_logger import get_auto_logger, augment_integration_hook
        from augment_conversation_manager import get_global_logger
        
        print("\n🔧 Initialisation du système...")
        
        # Démarrer l'auto-logger
        auto_logger = get_auto_logger()
        print(f"✅ Auto-logger démarré (Session: {auto_logger.logger.session_id[:8]}...)")
        
        # Configurer l'intégration
        integration_result = augment_integration_hook()
        print(f"✅ Intégration configurée: {integration_result['status']}")
        
        # Afficher les informations importantes
        print("\n📁 INFORMATIONS DE CONFIGURATION")
        print("-" * 40)
        print(f"📂 Dossier de sauvegarde: {auto_logger.logger.base_dir}")
        print(f"📝 Fichier de monitoring: {integration_result['monitor_file']}")
        print(f"🆔 Session ID: {integration_result['session_id'][:8]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        return False

def show_usage_examples():
    """Affiche des exemples d'utilisation"""
    print("\n💡 COMMENT UTILISER LE SYSTÈME")
    print("=" * 40)
    
    print("\n🔄 1. ENREGISTREMENT AUTOMATIQUE")
    print("   Le système surveille automatiquement les conversations.")
    print("   Aucune action requise de votre part !")
    
    print("\n✋ 2. ENREGISTREMENT MANUEL")
    print("   Dans vos scripts Python :")
    print("   ```python")
    print("   from augment_auto_logger import quick_log_conversation")
    print("   ")
    print("   quick_log_conversation(")
    print("       'Ma question à Augment',")
    print("       'Réponse d\\'Augment'")
    print("   )")
    print("   ```")
    
    print("\n🔍 3. RECHERCHE DANS L'HISTORIQUE")
    print("   python augment_conversation_manager.py --search 'terme'")
    
    print("\n📤 4. EXPORT DES CONVERSATIONS")
    print("   python augment_conversation_manager.py --export --format markdown")
    
    print("\n📊 5. STATISTIQUES")
    print("   python augment_conversation_manager.py --stats")

def create_quick_access_scripts():
    """Crée des scripts d'accès rapide"""
    print("\n🛠️ Création des scripts d'accès rapide...")
    
    # Script de recherche rapide
    search_script = """@echo off
echo 🔍 Recherche dans les conversations Augment
set /p query="Terme à rechercher: "
python augment_conversation_manager.py --search "%query%"
pause
"""
    
    with open("search_conversations.bat", "w", encoding="utf-8") as f:
        f.write(search_script)
    
    # Script d'export rapide
    export_script = """@echo off
echo 📤 Export des conversations Augment
echo.
echo Formats disponibles:
echo 1. JSON
echo 2. Markdown
echo 3. TXT
echo.
set /p choice="Choisissez le format (1-3): "

if "%choice%"=="1" (
    python augment_conversation_manager.py --export --format json
) else if "%choice%"=="2" (
    python augment_conversation_manager.py --export --format markdown
) else if "%choice%"=="3" (
    python augment_conversation_manager.py --export --format txt
) else (
    echo Format invalide
)
pause
"""
    
    with open("export_conversations.bat", "w", encoding="utf-8") as f:
        f.write(export_script)
    
    # Script de statistiques
    stats_script = """@echo off
echo 📊 Statistiques des conversations Augment
python augment_conversation_manager.py --stats
pause
"""
    
    with open("show_stats.bat", "w", encoding="utf-8") as f:
        f.write(stats_script)
    
    print("✅ Scripts créés:")
    print("   - search_conversations.bat (recherche rapide)")
    print("   - export_conversations.bat (export rapide)")
    print("   - show_stats.bat (statistiques)")

def test_system():
    """Test rapide du système"""
    print("\n🧪 Test rapide du système...")
    
    try:
        from augment_auto_logger import quick_log_conversation
        
        # Enregistrer une conversation de test
        quick_log_conversation(
            "Test du système de sauvegarde",
            "Le système de sauvegarde fonctionne correctement ! Toutes vos conversations avec Augment seront maintenant sauvegardées automatiquement."
        )
        
        print("✅ Test réussi - Conversation de test enregistrée")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def main():
    """Fonction principale"""
    print("🗣️ SYSTÈME DE SAUVEGARDE DES CONVERSATIONS AUGMENT")
    print("=" * 60)
    print("Ce script configure la sauvegarde automatique de vos conversations")
    print("avec Augment Agent dans Visual Studio Code.")
    print()
    
    # Configuration du système
    if not setup_conversation_logging():
        print("\n❌ Échec de la configuration")
        input("Appuyez sur Entrée pour quitter...")
        return
    
    # Test du système
    if not test_system():
        print("\n⚠️ Le système est configuré mais le test a échoué")
    
    # Créer les scripts d'accès rapide
    create_quick_access_scripts()
    
    # Afficher les exemples d'utilisation
    show_usage_examples()
    
    print("\n🎉 CONFIGURATION TERMINÉE !")
    print("=" * 40)
    print("✅ Le système de sauvegarde est maintenant actif")
    print("📁 Vos conversations seront sauvegardées dans: augment_conversations/")
    print("🔄 La sauvegarde est automatique - aucune action requise")
    
    print("\n🚀 PROCHAINES ÉTAPES:")
    print("1. Continuez à utiliser Augment normalement")
    print("2. Vos conversations seront automatiquement sauvegardées")
    print("3. Utilisez les scripts .bat pour rechercher/exporter")
    print("4. Consultez README_CONVERSATION_LOGGER.md pour plus d'infos")
    
    # Proposer d'ouvrir le dossier de sauvegarde
    response = input("\n📂 Voulez-vous ouvrir le dossier de sauvegarde ? (o/n): ").lower()
    if response in ['o', 'oui', 'y', 'yes']:
        try:
            import subprocess
            subprocess.run(['explorer', 'augment_conversations'], check=True)
        except:
            print("❌ Impossible d'ouvrir le dossier automatiquement")
            print(f"📁 Ouvrez manuellement: {Path('augment_conversations').absolute()}")
    
    input("\nAppuyez sur Entrée pour terminer...")

if __name__ == "__main__":
    main()
