"""
🗣️ AUGMENT CONVERSATION LOGGER
Système de sauvegarde locale des conversations avec Augment Agent

Fonctionnalités :
- Sauvegarde automatique des conversations
- Organisation par date et session
- Format JSON structuré
- Recherche dans l'historique
- Export et import des données
"""

import json
import os
import datetime
import hashlib
import uuid
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

# Configuration du logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ConversationMessage:
    """Structure d'un message de conversation"""
    timestamp: str
    message_id: str
    role: str  # 'user' ou 'assistant'
    content: str
    metadata: Dict[str, Any]
    session_id: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit en dictionnaire"""
        return asdict(self)

@dataclass
class ConversationSession:
    """Structure d'une session de conversation"""
    session_id: str
    start_time: str
    end_time: Optional[str]
    workspace_path: str
    total_messages: int
    messages: List[ConversationMessage]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit en dictionnaire"""
        return {
            'session_id': self.session_id,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'workspace_path': self.workspace_path,
            'total_messages': self.total_messages,
            'messages': [msg.to_dict() for msg in self.messages]
        }

class AugmentConversationLogger:
    """
    🗣️ LOGGER PRINCIPAL POUR CONVERSATIONS AUGMENT
    
    Sauvegarde automatique et organisation des conversations
    """
    
    def __init__(self, base_dir: str = "augment_conversations"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # Session actuelle
        self.current_session: Optional[ConversationSession] = None
        self.session_id = str(uuid.uuid4())
        
        # Configuration
        self.config = {
            'auto_save': True,
            'save_frequency': 1,  # Sauvegarder après chaque message
            'max_sessions_per_file': 50,
            'compress_old_files': True,
            'backup_enabled': True
        }
        
        # Compteurs
        self.message_count = 0
        self.session_count = 0
        
        logger.info(f"🗣️ Augment Conversation Logger initialisé")
        logger.info(f"📁 Dossier de sauvegarde: {self.base_dir.absolute()}")
        
        # Créer la structure de dossiers
        self._create_directory_structure()
        
        # Démarrer une nouvelle session
        self._start_new_session()
    
    def _create_directory_structure(self):
        """Crée la structure de dossiers pour l'organisation"""
        directories = [
            'daily',      # Conversations par jour
            'sessions',   # Sessions complètes
            'backups',    # Sauvegardes
            'exports',    # Exports personnalisés
            'search_index'  # Index pour recherche rapide
        ]
        
        for dir_name in directories:
            (self.base_dir / dir_name).mkdir(exist_ok=True)
    
    def _start_new_session(self):
        """Démarre une nouvelle session de conversation"""
        self.session_id = str(uuid.uuid4())
        current_time = datetime.datetime.now().isoformat()
        
        # Obtenir le workspace actuel (si possible)
        workspace_path = os.getcwd()
        
        self.current_session = ConversationSession(
            session_id=self.session_id,
            start_time=current_time,
            end_time=None,
            workspace_path=workspace_path,
            total_messages=0,
            messages=[]
        )
        
        logger.info(f"🆕 Nouvelle session démarrée: {self.session_id[:8]}...")
    
    def log_message(self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None):
        """
        📝 ENREGISTRE UN MESSAGE DE CONVERSATION
        
        Args:
            role: 'user' ou 'assistant'
            content: Contenu du message
            metadata: Métadonnées additionnelles
        """
        if not self.current_session:
            self._start_new_session()
        
        # Créer le message
        message_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        
        message = ConversationMessage(
            timestamp=timestamp,
            message_id=message_id,
            role=role,
            content=content,
            metadata=metadata or {},
            session_id=self.session_id
        )
        
        # Ajouter à la session
        self.current_session.messages.append(message)
        self.current_session.total_messages += 1
        self.message_count += 1
        
        logger.info(f"💬 Message enregistré: {role} ({len(content)} caractères)")
        
        # Sauvegarde automatique
        if self.config['auto_save'] and self.message_count % self.config['save_frequency'] == 0:
            self._auto_save()
    
    def log_user_message(self, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Enregistre un message utilisateur"""
        self.log_message('user', content, metadata)
    
    def log_assistant_message(self, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Enregistre un message assistant"""
        self.log_message('assistant', content, metadata)
    
    def _auto_save(self):
        """Sauvegarde automatique de la session actuelle"""
        if not self.current_session:
            return
        
        try:
            # Nom de fichier basé sur la date
            today = datetime.datetime.now().strftime("%Y%m%d")
            session_file = self.base_dir / 'sessions' / f"session_{today}_{self.session_id[:8]}.json"
            
            # Sauvegarder la session
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_session.to_dict(), f, indent=2, ensure_ascii=False)
            
            # Sauvegarder aussi dans le fichier quotidien
            daily_file = self.base_dir / 'daily' / f"conversations_{today}.json"
            self._append_to_daily_file(daily_file)
            
            logger.info(f"💾 Session sauvegardée: {session_file.name}")
            
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde: {e}")
    
    def _append_to_daily_file(self, daily_file: Path):
        """Ajoute la session au fichier quotidien"""
        try:
            # Charger le fichier existant ou créer nouveau
            if daily_file.exists():
                with open(daily_file, 'r', encoding='utf-8') as f:
                    daily_data = json.load(f)
            else:
                daily_data = {
                    'date': datetime.datetime.now().strftime("%Y-%m-%d"),
                    'sessions': []
                }
            
            # Ajouter ou mettre à jour la session
            session_dict = self.current_session.to_dict()
            
            # Chercher si la session existe déjà
            session_found = False
            for i, existing_session in enumerate(daily_data['sessions']):
                if existing_session['session_id'] == self.session_id:
                    daily_data['sessions'][i] = session_dict
                    session_found = True
                    break
            
            if not session_found:
                daily_data['sessions'].append(session_dict)
            
            # Sauvegarder
            with open(daily_file, 'w', encoding='utf-8') as f:
                json.dump(daily_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"❌ Erreur fichier quotidien: {e}")

    def end_session(self):
        """Termine la session actuelle"""
        if self.current_session:
            self.current_session.end_time = datetime.datetime.now().isoformat()
            self._auto_save()
            logger.info(f"🏁 Session terminée: {self.session_id[:8]}...")
            self.current_session = None

    def search_conversations(self, query: str, date_range: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        🔍 RECHERCHE DANS L'HISTORIQUE DES CONVERSATIONS

        Args:
            query: Terme à rechercher
            date_range: Tuple (date_debut, date_fin) optionnel

        Returns:
            Liste des messages correspondants
        """
        results = []

        try:
            # Parcourir tous les fichiers quotidiens
            daily_dir = self.base_dir / 'daily'

            for daily_file in daily_dir.glob('conversations_*.json'):
                try:
                    with open(daily_file, 'r', encoding='utf-8') as f:
                        daily_data = json.load(f)

                    # Filtrer par date si spécifié
                    if date_range:
                        file_date = daily_data.get('date', '')
                        if not (date_range[0] <= file_date <= date_range[1]):
                            continue

                    # Rechercher dans les sessions
                    for session in daily_data.get('sessions', []):
                        for message in session.get('messages', []):
                            if query.lower() in message.get('content', '').lower():
                                results.append({
                                    'session_id': session['session_id'],
                                    'date': daily_data['date'],
                                    'message': message,
                                    'workspace': session.get('workspace_path', '')
                                })

                except Exception as e:
                    logger.error(f"❌ Erreur lecture {daily_file}: {e}")

            logger.info(f"🔍 Recherche '{query}': {len(results)} résultats trouvés")
            return results

        except Exception as e:
            logger.error(f"❌ Erreur recherche: {e}")
            return []

    def export_conversations(self, output_file: str, date_range: Optional[tuple] = None, format_type: str = 'json'):
        """
        📤 EXPORT DES CONVERSATIONS

        Args:
            output_file: Fichier de sortie
            date_range: Plage de dates optionnelle
            format_type: 'json', 'txt', ou 'markdown'
        """
        try:
            export_data = []

            # Collecter les données
            daily_dir = self.base_dir / 'daily'
            for daily_file in daily_dir.glob('conversations_*.json'):
                with open(daily_file, 'r', encoding='utf-8') as f:
                    daily_data = json.load(f)

                # Filtrer par date si nécessaire
                if date_range:
                    file_date = daily_data.get('date', '')
                    if not (date_range[0] <= file_date <= date_range[1]):
                        continue

                export_data.append(daily_data)

            # Exporter selon le format
            export_path = self.base_dir / 'exports' / output_file

            if format_type == 'json':
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

            elif format_type == 'txt':
                with open(export_path, 'w', encoding='utf-8') as f:
                    for day_data in export_data:
                        f.write(f"=== {day_data['date']} ===\n\n")
                        for session in day_data.get('sessions', []):
                            f.write(f"Session: {session['session_id'][:8]}...\n")
                            f.write(f"Workspace: {session.get('workspace_path', 'N/A')}\n\n")

                            for msg in session.get('messages', []):
                                f.write(f"[{msg['timestamp']}] {msg['role'].upper()}:\n")
                                f.write(f"{msg['content']}\n\n")
                            f.write("-" * 50 + "\n\n")

            elif format_type == 'markdown':
                with open(export_path, 'w', encoding='utf-8') as f:
                    f.write("# Historique des Conversations Augment\n\n")

                    for day_data in export_data:
                        f.write(f"## {day_data['date']}\n\n")

                        for session in day_data.get('sessions', []):
                            f.write(f"### Session {session['session_id'][:8]}...\n\n")
                            f.write(f"**Workspace:** `{session.get('workspace_path', 'N/A')}`\n\n")

                            for msg in session.get('messages', []):
                                role_icon = "👤" if msg['role'] == 'user' else "🤖"
                                f.write(f"{role_icon} **{msg['role'].title()}** _{msg['timestamp']}_\n\n")
                                f.write(f"{msg['content']}\n\n")
                                f.write("---\n\n")

            logger.info(f"📤 Export terminé: {export_path}")

        except Exception as e:
            logger.error(f"❌ Erreur export: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """
        📊 STATISTIQUES DES CONVERSATIONS

        Returns:
            Dictionnaire avec les statistiques
        """
        try:
            stats = {
                'total_sessions': 0,
                'total_messages': 0,
                'user_messages': 0,
                'assistant_messages': 0,
                'date_range': {'first': None, 'last': None},
                'workspaces': set(),
                'daily_breakdown': {}
            }

            # Parcourir tous les fichiers quotidiens
            daily_dir = self.base_dir / 'daily'
            dates = []

            for daily_file in daily_dir.glob('conversations_*.json'):
                try:
                    with open(daily_file, 'r', encoding='utf-8') as f:
                        daily_data = json.load(f)

                    date = daily_data.get('date', '')
                    dates.append(date)

                    day_sessions = len(daily_data.get('sessions', []))
                    day_messages = 0
                    day_user_msgs = 0
                    day_assistant_msgs = 0

                    stats['total_sessions'] += day_sessions

                    for session in daily_data.get('sessions', []):
                        # Workspace
                        workspace = session.get('workspace_path', '')
                        if workspace:
                            stats['workspaces'].add(workspace)

                        # Messages
                        for msg in session.get('messages', []):
                            day_messages += 1
                            if msg['role'] == 'user':
                                day_user_msgs += 1
                            else:
                                day_assistant_msgs += 1

                    stats['total_messages'] += day_messages
                    stats['user_messages'] += day_user_msgs
                    stats['assistant_messages'] += day_assistant_msgs

                    stats['daily_breakdown'][date] = {
                        'sessions': day_sessions,
                        'messages': day_messages,
                        'user_messages': day_user_msgs,
                        'assistant_messages': day_assistant_msgs
                    }

                except Exception as e:
                    logger.error(f"❌ Erreur lecture stats {daily_file}: {e}")

            # Plage de dates
            if dates:
                dates.sort()
                stats['date_range']['first'] = dates[0]
                stats['date_range']['last'] = dates[-1]

            # Convertir set en liste pour JSON
            stats['workspaces'] = list(stats['workspaces'])

            return stats

        except Exception as e:
            logger.error(f"❌ Erreur calcul statistiques: {e}")
            return {}
