#!/usr/bin/env python3
"""
Script pour analyser le fonctionnement des indices dans une partie complète
"""

import sys
sys.path.append('.')
from azr_baccarat_predictor import AZRBaccaratPredictor, BaccaratGenerator

def analyze_indices_in_game():
    """Génère une partie et analyse le comportement des indices"""

    # Créer le générateur de parties
    generator = BaccaratGenerator()

    print('🎮 GÉNÉRATION D\'UNE PARTIE COMPLÈTE POUR ANALYSER LES INDICES')
    print('=' * 70)

    # Générer la partie
    game_data = generator.generate_game_data(target_pb_hands=15)  # Limiter à 15 manches P/B
    
    print(f'📊 PARTIE GÉNÉRÉE:')
    print(f'   - Manches totales: {len(game_data["hands"])}')
    print(f'   - Manches P/B: {game_data["pb_hands"]}')
    print(f'   - Manches TIE: {game_data["tie_hands"]}')
    print()
    
    print('📋 DÉTAIL DES MANCHES AVEC INDICES:')
    print('=' * 70)
    print('Main | Résultat | P/B# | Parité | Sync   | Combiné     | S/O')
    print('-' * 70)
    
    for i, hand in enumerate(game_data['hands']):
        main_num = i + 1
        result = hand['result']
        pb_num = str(hand.get('pb_hand_number', '--'))
        parity = hand.get('parity', '--')
        sync = hand.get('sync_state', '--')
        combined = hand.get('combined_state', '--')
        so = hand.get('so_conversion', '--')

        print(f'{main_num:4d} | {result:8s} | {pb_num:4s} | {parity:6s} | {sync:6s} | {combined:11s} | {so:2s}')
    
    print()
    print('🔍 ANALYSE DES SÉQUENCES:')
    print('=' * 70)
    
    # Extraire les séquences
    all_results = [h['result'] for h in game_data['hands']]
    pb_results = [h['result'] for h in game_data['hands'] if h['result'] in ['PLAYER', 'BANKER']]
    parities = [h.get('parity', '') for h in game_data['hands'] if h.get('parity')]
    sync_states = [h.get('sync_state', '') for h in game_data['hands'] if h.get('sync_state')]
    combined_states = [h.get('combined_state', '') for h in game_data['hands'] if h.get('combined_state')]
    so_conversions = [h.get('so_conversion', '') for h in game_data['hands'] if h.get('so_conversion') in ['S', 'O']]
    
    print(f'Séquence complète: {all_results}')
    print(f'Séquence P/B:      {pb_results}')
    print(f'Séquence parités:  {parities}')
    print(f'Séquence sync:     {sync_states}')
    print(f'Séquence combiné:  {combined_states}')
    print(f'Séquence S/O:      {so_conversions}')
    
    print()
    print('🧠 ANALYSE DÉTAILLÉE DES INDICES:')
    print('=' * 70)
    
    # Analyser les patterns
    print('INDEX 1 (IMPAIR/PAIR):')
    print(f'  - Basé sur: Nombre de cartes utilisées par manche')
    print(f'  - Séquence: {parities}')
    print(f'  - Pattern: {" → ".join(parities)}')
    
    print()
    print('INDEX 2 (SYNC/DESYNC):')
    print(f'  - Basé sur: Changement d\'état selon parité')
    print(f'  - Séquence: {sync_states}')
    print(f'  - Pattern: {" → ".join(sync_states)}')
    
    print()
    print('INDEX 3 (COMBINÉ):')
    print(f'  - Basé sur: Combinaison Index 1 + Index 2')
    print(f'  - Séquence: {combined_states}')
    print(f'  - Pattern: {" → ".join(combined_states)}')
    
    print()
    print('INDEX 4 (P/B/T):')
    print(f'  - Basé sur: Résultats des manches')
    print(f'  - Séquence complète: {all_results}')
    print(f'  - Séquence P/B seulement: {pb_results}')
    print(f'  - Pattern: {" → ".join(all_results)}')
    
    print()
    print('INDEX 5 (S/O):')
    print(f'  - Basé sur: Comparaison résultats consécutifs P/B')
    print(f'  - Séquence: {so_conversions}')
    print(f'  - Pattern: {" → ".join(so_conversions)}')
    
    print()
    print('🎯 OBSERVATIONS CRITIQUES:')
    print('=' * 70)
    
    # Compter les TIE
    tie_count = sum(1 for h in game_data['hands'] if h['result'] == 'TIE')
    if tie_count > 0:
        print(f'⚠️  {tie_count} TIE détectés dans cette partie')
        print(f'   - Impact sur Index 1-3: Mis à jour normalement')
        print(f'   - Impact sur Index 4: Inclus dans la séquence P/B/T')
        print(f'   - Impact sur Index 5: Pas de conversion S/O pour TIE')
    else:
        print('✅ Aucun TIE dans cette partie')
    
    return game_data

if __name__ == "__main__":
    analyze_indices_in_game()
