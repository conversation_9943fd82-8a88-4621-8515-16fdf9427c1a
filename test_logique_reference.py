#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 TEST DE LA LOGIQUE DE RÉFÉRENCE AZR BACCARAT

Test pour vérifier que le programme suit exactement la logique de référence :
- Compteur manche = nombre manches P/B terminées
- TIE ne termine pas les manches
- Index 1-3 toujours alimentés
- Index 4 inclut P/B/T
- Index 5 seulement après 2 manches P/B
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azr_baccarat_predictor import AZRBaccaratInterface, AZRBaccaratPredictor, AZRConfig

def test_logique_reference():
    """Test de la logique de référence avec l'exemple exact"""
    
    print("🎯 TEST DE LA LOGIQUE DE RÉFÉRENCE AZR BACCARAT")
    print("=" * 60)
    
    # Initialiser le système
    config = AZRConfig()
    predictor = AZRBaccaratPredictor(config)
    interface = AZRBaccaratInterface(predictor)
    
    print(f"📊 État initial:")
    print(f"   - Compteur P/B: {interface.current_pb_number}")
    print(f"   - Affichage attendu: Manche P/B: 1")
    print()
    
    # BRÛLAGE
    print("🔥 PHASE BRÛLAGE")
    interface.initialiser_burn('PAIR')
    print(f"   - Brûlage: PAIR")
    print(f"   - Compteur P/B: {interface.current_pb_number} (doit rester 0)")
    print(f"   - État sync: {interface.current_sync_state}")
    print()
    
    # MAIN 1 (Manche 1) : TIE IMPAIR
    print("🎮 MAIN 1 (Manche 1) : TIE IMPAIR")
    print(f"   - Avant: Compteur P/B = {interface.current_pb_number}")
    interface.process_hand('TIE', 'IMPAIR')
    print(f"   - Après: Compteur P/B = {interface.current_pb_number} (doit rester 0)")
    print(f"   - État sync: {interface.current_sync_state}")
    print()
    
    # MAIN 2 (Manche 1 suite) : PLAYER PAIR
    print("🎮 MAIN 2 (Manche 1 suite) : PLAYER PAIR")
    print(f"   - Avant: Compteur P/B = {interface.current_pb_number}")
    interface.process_hand('PLAYER', 'PAIR')
    print(f"   - Après: Compteur P/B = {interface.current_pb_number} (doit être 1)")
    print(f"   - État sync: {interface.current_sync_state}")
    print()
    
    # MAIN 3 (Manche 2) : BANKER IMPAIR
    print("🎮 MAIN 3 (Manche 2) : BANKER IMPAIR")
    print(f"   - Avant: Compteur P/B = {interface.current_pb_number}")
    interface.process_hand('BANKER', 'IMPAIR')
    print(f"   - Après: Compteur P/B = {interface.current_pb_number} (doit être 2)")
    print(f"   - État sync: {interface.current_sync_state}")
    print()
    
    # ANALYSE DES DONNÉES
    print("📊 ANALYSE DES DONNÉES ENREGISTRÉES")
    print("=" * 60)
    
    hands = interface.current_game['hands']
    print(f"Nombre total de mains: {len(hands)}")
    print(f"Statistiques P/B: {interface.current_game['statistics']['pb_hands']}")
    print(f"Statistiques TIE: {interface.current_game['statistics']['tie_hands']}")
    print()
    
    print("📋 DÉTAIL DES MAINS:")
    for i, hand in enumerate(hands, 1):
        print(f"   Main {i}: {hand['result']} {hand['parity']} "
              f"(Manche P/B: {hand['pb_hand_number']}, "
              f"Sync: {hand['sync_state']}, "
              f"S/O: {hand['so_conversion']})")
    print()
    
    # VALIDATION
    print("✅ VALIDATION DE LA LOGIQUE")
    print("=" * 60)
    
    # Vérifications
    expected_pb_count = 2
    expected_tie_count = 1
    expected_total = 3
    
    actual_pb_count = interface.current_game['statistics']['pb_hands']
    actual_tie_count = interface.current_game['statistics']['tie_hands']
    actual_total = len(hands)
    
    print(f"✅ Compteur P/B final: {interface.current_pb_number} (attendu: 2)")
    print(f"✅ Manches P/B enregistrées: {actual_pb_count} (attendu: {expected_pb_count})")
    print(f"✅ Manches TIE enregistrées: {actual_tie_count} (attendu: {expected_tie_count})")
    print(f"✅ Total mains: {actual_total} (attendu: {expected_total})")
    
    # Vérification des numéros de manche
    print()
    print("🔍 VÉRIFICATION NUMÉROS DE MANCHE:")
    expected_pb_numbers = [0, 0, 1]  # TIE reste 0, PLAYER termine manche 0→1, BANKER termine manche 1→2
    for i, hand in enumerate(hands):
        expected = expected_pb_numbers[i]
        actual = hand['pb_hand_number']
        status = "✅" if actual == expected else "❌"
        print(f"   {status} Main {i+1}: Manche P/B {actual} (attendu: {expected})")
    
    # Vérification des conversions S/O
    print()
    print("🔍 VÉRIFICATION CONVERSIONS S/O:")
    expected_so = ['--', '--', 'O']  # TIE='--, PLAYER='--, BANKER=O (PLAYER→BANKER)
    for i, hand in enumerate(hands):
        expected = expected_so[i]
        actual = hand['so_conversion']
        status = "✅" if actual == expected else "❌"
        print(f"   {status} Main {i+1}: S/O {actual} (attendu: {expected})")
    
    print()
    print("🎯 TEST TERMINÉ")
    
    # Retourner les résultats pour validation
    return {
        'pb_count': actual_pb_count,
        'tie_count': actual_tie_count,
        'total_hands': actual_total,
        'final_pb_number': interface.current_pb_number,
        'hands': hands
    }

if __name__ == "__main__":
    test_logique_reference()
