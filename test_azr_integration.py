#!/usr/bin/env python3
"""
Test d'intégration complet du système AZR
Test avec génération de partie et prédictions en temps réel
"""

import sys
import time
from azr_baccarat_predictor import AZRBaccaratPredictor

def test_azr_integration():
    """Test d'intégration complet avec génération et prédictions"""
    print("🚀 TEST D'INTÉGRATION SYSTÈME AZR")
    print("=" * 50)
    
    try:
        # Initialisation du système AZR
        print("\n🔧 Initialisation du système AZR...")
        predictor = AZRBaccaratPredictor()
        print("   ✅ Système AZR initialisé")
        
        # Génération d'une partie de test
        print("\n🎲 Génération partie de test...")
        game_data = predictor.generator.generate_game_data(target_pb_hands=10)
        
        print(f"   📊 Partie générée:")
        print(f"      - Cartes brûlées: {game_data['burn_cards_count']}")
        print(f"      - <PERSON><PERSON><PERSON> brûlage: {game_data['burn_parity']}")
        print(f"      - Total manches: {game_data['total_hands']}")
        print(f"      - Manches P/B: {game_data['pb_hands']}")
        print(f"      - Manches TIE: {game_data['tie_hands']}")
        
        # Test prédictions en temps réel
        print("\n🔮 Test prédictions en temps réel...")
        print("   Manche | Résultat | Parité | Sync | Combiné | Prédiction")
        print("   " + "-" * 60)
        
        predictions = []
        correct_predictions = 0
        total_predictions = 0
        
        for i, hand in enumerate(game_data['hands']):
            # Préparer les données de la manche
            hand_data = {
                'pb_hand_number': hand.get('pb_hand_number', i + 1),
                'result': hand['result'],
                'parity': hand['parity'],
                'sync_state': hand.get('sync_state', 'SYNC'),
                'so_conversion': hand.get('so_conversion', '--')
            }
            
            # Obtenir prédiction pour la manche suivante
            if hand['result'] in ['PLAYER', 'BANKER']:  # Seulement pour manches P/B
                prediction = predictor.receive_hand_data(hand_data)
                predictions.append(prediction)
                
                # Affichage
                combined_state = f"{hand['parity']}_{hand.get('sync_state', 'SYNC')}"
                print(f"   {hand_data['pb_hand_number']:6} | {hand['result']:8} | {hand['parity']:6} | {hand.get('sync_state', 'SYNC'):4} | {combined_state:11} | {prediction}")
                
                # Validation si on a une prédiction précédente
                if len(predictions) > 1 and hand.get('so_conversion') in ['S', 'O']:
                    prev_prediction = predictions[-2]
                    actual = hand['so_conversion']
                    is_correct = (prev_prediction == actual)
                    
                    if is_correct:
                        correct_predictions += 1
                    total_predictions += 1
                    
                    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
                    print(f"      → Validation: Prédit={prev_prediction}, Réel={actual}, Correct={is_correct}, Précision={accuracy:.1%}")
        
        # Résumé des performances
        print(f"\n📈 Résumé des performances:")
        if total_predictions > 0:
            final_accuracy = correct_predictions / total_predictions
            print(f"   - Prédictions validées: {total_predictions}")
            print(f"   - Prédictions correctes: {correct_predictions}")
            print(f"   - Précision finale: {final_accuracy:.1%}")
        else:
            print("   - Pas assez de données pour validation")
        
        print(f"   - Prédictions générées: {len(predictions)}")
        
        # Test des métriques système
        print(f"\n📊 Métriques système AZR:")
        print(f"   - Historique manches: {len(predictor.hands_history)}")
        print(f"   - Séquence PAIR/IMPAIR: {len(predictor.sequence_history)}")
        print(f"   - État sync actuel: {predictor.current_sync_state}")
        print(f"   - Précision globale: {predictor.current_accuracy:.3f}")
        print(f"   - Total prédictions: {predictor.total_predictions}")
        print(f"   - Clusters AZR Master: {len(predictor.azr_master.clusters)}")

        # Test statut système AZR Master
        master_status = predictor.azr_master.get_system_status()
        print(f"   - Architecture: {master_status['architecture']['communication_model']}")
        print(f"   - Total rollouts: {master_status['architecture']['total_rollouts']}")

        print(f"\n✅ Test d'intégration réussi !")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur test d'intégration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_azr_master_system():
    """Test du système Master AZR avec clusters"""
    print("\n🎯 TEST SYSTÈME MASTER AZR")
    print("=" * 50)
    
    try:
        from azr_baccarat_predictor import AZRMaster, AZRConfig, BaccaratHand
        
        # Initialisation Master AZR
        print("\n🔧 Initialisation Master AZR...")
        config = AZRConfig()
        master = AZRMaster(config)
        print(f"   ✅ Master initialisé avec {len(master.clusters)} clusters")
        
        # Test données simulées
        print("\n📊 Test avec données simulées...")
        test_sequence = {
            'hands_history': [
                BaccaratHand(1, 'PLAYER', 'PAIR', 'SYNC', 'S', 'PAIR_SYNC'),
                BaccaratHand(2, 'BANKER', 'IMPAIR', 'DESYNC', 'O', 'IMPAIR_DESYNC'),
                BaccaratHand(3, 'PLAYER', 'PAIR', 'DESYNC', 'S', 'PAIR_DESYNC'),
                BaccaratHand(4, 'BANKER', 'IMPAIR', 'SYNC', 'O', 'IMPAIR_SYNC'),
                BaccaratHand(5, 'PLAYER', 'PAIR', 'SYNC', 'S', 'PAIR_SYNC')
            ]
        }
        
        # Test statut système
        status = master.get_system_status()
        print(f"   📈 Statut système:")
        print(f"      - Clusters actifs: {len(status['cluster_status'])}")
        print(f"      - Architecture: {status['architecture']['communication_model']}")
        print(f"      - Total rollouts: {status['architecture']['total_rollouts']}")
        
        print(f"\n✅ Test Master AZR réussi !")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur test Master AZR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Exécute tous les tests d'intégration"""
    print("🧪 TESTS D'INTÉGRATION SYSTÈME AZR COMPLET")
    print("=" * 60)
    
    start_time = time.time()
    
    # Test 1: Intégration complète
    test1_success = test_azr_integration()
    
    # Test 2: Système Master AZR
    test2_success = test_azr_master_system()
    
    # Résumé final
    total_time = time.time() - start_time
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ TESTS D'INTÉGRATION")
    print("=" * 60)
    
    tests_results = [
        ("Intégration complète", test1_success),
        ("Système Master AZR", test2_success)
    ]
    
    passed = sum(1 for _, success in tests_results if success)
    total = len(tests_results)
    
    for test_name, success in tests_results:
        status = "✅ PASSÉ" if success else "❌ ÉCHEC"
        print(f"{test_name:.<40} {status}")
    
    print(f"\n🎯 Résultat global: {passed}/{total} tests réussis")
    print(f"⏱️  Temps total: {total_time:.2f}s")
    
    if passed == total:
        print("\n🚀 SYSTÈME AZR COMPLÈTEMENT OPÉRATIONNEL!")
        print("   Prêt pour utilisation en production")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) en échec")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
